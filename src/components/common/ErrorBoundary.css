/* Error Boundary Styles */
.error-boundary {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.error-boundary h2 {
  color: var(--danger-red);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
}

.error-boundary p {
  color: var(--medium-gray);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.error-details {
  text-align: left;
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  color: var(--danger-red);
  margin-bottom: var(--spacing-sm);
}

.error-details pre {
  font-size: 0.8rem;
  color: var(--dark-gray);
  white-space: pre-wrap;
  word-break: break-word;
  margin: var(--spacing-sm) 0;
  padding: var(--spacing-sm);
  background-color: var(--white);
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .btn {
  min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
  .error-boundary {
    padding: var(--spacing-lg);
    min-height: 300px;
  }

  .error-boundary-content {
    max-width: 100%;
  }

  .error-icon {
    font-size: 3rem;
  }

  .error-boundary h2 {
    font-size: 1.25rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions .btn {
    width: 100%;
    max-width: 200px;
  }
}

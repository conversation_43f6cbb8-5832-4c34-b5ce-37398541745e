.breach-monitor {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.breach-monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.breach-monitor-header h2 {
  margin: 0;
  color: var(--dark-gray);
  font-size: 1.25rem;
}

.breach-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.filter-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--white);
}

.breach-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.breach-stat-card {
  background-color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  text-align: center;
  position: relative;
}

.breach-stat-card.critical {
  border-left: 4px solid var(--danger-red);
}

.breach-stat-card.warning {
  border-left: 4px solid var(--warning-orange);
}

.breach-stat-card.normal {
  border-left: 4px solid var(--success-green);
}

.breach-stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.breach-stat-value.critical {
  color: var(--danger-red);
}

.breach-stat-value.warning {
  color: var(--warning-orange);
}

.breach-stat-value.normal {
  color: var(--success-green);
}

.breach-stat-label {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: var(--spacing-xs) 0 0 0;
}

.breach-stat-trend {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: var(--danger-red);
}

.trend-down {
  color: var(--success-green);
}

.trend-neutral {
  color: var(--medium-gray);
}

.active-breaches {
  flex: 1;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.active-breaches-header {
  background-color: var(--light-gray);
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.active-breaches-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.live-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger-red);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

.breach-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.breach-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.breach-item:hover {
  background-color: var(--light-gray);
}

.breach-item:last-child {
  border-bottom: none;
}

.breach-item.critical {
  border-left: 4px solid var(--danger-red);
  background-color: #fef8f8;
}

.breach-item.warning {
  border-left: 4px solid var(--warning-orange);
  background-color: #fff9f5;
}

.breach-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.breach-zone {
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 14px;
}

.breach-time {
  font-size: 12px;
  color: var(--medium-gray);
}

.breach-details {
  font-size: 13px;
  color: var(--dark-gray);
  margin-bottom: var(--spacing-xs);
}

.breach-location {
  font-size: 12px;
  color: var(--medium-gray);
  font-family: monospace;
}

.breach-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.breach-action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.acknowledge-btn {
  background-color: var(--primary-blue);
  color: var(--white);
}

.locate-btn {
  background-color: var(--success-green);
  color: var(--white);
}

.alert-btn {
  background-color: var(--warning-orange);
  color: var(--white);
}

.breach-action-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.empty-breaches {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--medium-gray);
}

.empty-breaches h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--success-green);
}

.empty-breaches p {
  margin: 0;
  font-size: 14px;
}

.breach-timeline {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
}

.timeline-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid #e9ecef;
}

.timeline-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-gray);
}

.timeline-period {
  font-size: 12px;
  color: var(--medium-gray);
}

.timeline-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-gray);
  border-radius: 4px;
  color: var(--medium-gray);
}

/* Responsive design */
@media (max-width: 768px) {
  .breach-monitor {
    padding: var(--spacing-sm);
  }
  
  .breach-monitor-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .breach-controls {
    justify-content: center;
  }
  
  .breach-stats {
    grid-template-columns: 1fr;
  }
  
  .active-breaches-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
  
  .breach-header {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .breach-actions {
    flex-wrap: wrap;
  }
}

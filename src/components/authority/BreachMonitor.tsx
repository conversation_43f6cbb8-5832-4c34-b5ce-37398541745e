import React, { useState, useEffect } from 'react';
import { supabase } from '../../services/supabase';
import type { BreachEvent, ProtectedZone } from '../../types';
import LoadingSpinner from '../common/LoadingSpinner';
import './BreachMonitor.css';

interface BreachStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
}

const BreachMonitor: React.FC = () => {
  const [breaches, setBreaches] = useState<BreachEvent[]>([]);
  const [zones, setZones] = useState<ProtectedZone[]>([]);
  const [stats, setStats] = useState<BreachStats>({ total: 0, today: 0, thisWeek: 0, thisMonth: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [selectedZone, setSelectedZone] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
    
    // Set up real-time subscription for breach events
    const breachChannel = supabase
      .channel('breach-events')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'breach_events'
        },
        (payload) => {
          console.log('New breach event:', payload);
          handleNewBreach(payload.new as BreachEvent);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(breachChannel);
    };
  }, []);

  useEffect(() => {
    calculateStats();
  }, [breaches]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load breach events
      const { data: breachData, error: breachError } = await supabase
        .from('breach_events')
        .select(`
          *,
          protected_zones (
            id,
            name,
            protection_level
          ),
          user_profiles (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      if (breachError) throw breachError;

      // Load zones for filtering
      const { data: zoneData, error: zoneError } = await supabase
        .from('protected_zones')
        .select(`
          id,
          name,
          description,
          geometry,
          effective_from,
          effective_to,
          enforcement_notes,
          created_by,
          created_at,
          updated_at
        `)
        .eq('effective_to', null) // Only active zones
        .order('name');

      if (zoneError) throw zoneError;

      // Transform database zones to ProtectedZone format
      const transformedZones: ProtectedZone[] = (zoneData || []).map(zone => ({
        id: zone.id,
        type: 'Feature' as const,
        geometry: zone.geometry,
        properties: {
          name: zone.name,
          description: zone.description,
          effective_from: zone.effective_from,
          effective_to: zone.effective_to,
          enforcement_notes: zone.enforcement_notes,
          created_by: zone.created_by,
          created_at: zone.created_at,
          updated_at: zone.updated_at
        },
        // Legacy properties for backward compatibility
        name: zone.name,
        description: zone.description,
        coordinates: zone.geometry?.coordinates || [],
        isActive: !zone.effective_to
      }));

      setBreaches(breachData || []);
      setZones(transformedZones);
    } catch (err) {
      console.error('Error loading breach data:', err);
      setError('Failed to load breach monitoring data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewBreach = (newBreach: BreachEvent) => {
    setBreaches(prev => [newBreach, ...prev.slice(0, 99)]); // Keep last 100 events
    
    // Show notification for new breach
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Zone Breach Alert', {
        body: `New breach detected in protected zone`,
        icon: '/icon-192x192.png'
      });
    }
  };

  const calculateStats = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const stats = {
      total: breaches.length,
      today: breaches.filter(b => new Date(b.created_at) >= today).length,
      thisWeek: breaches.filter(b => new Date(b.created_at) >= thisWeek).length,
      thisMonth: breaches.filter(b => new Date(b.created_at) >= thisMonth).length
    };

    setStats(stats);
  };

  const getFilteredBreaches = () => {
    let filtered = breaches;

    // Filter by zone
    if (selectedZone !== 'all') {
      filtered = filtered.filter(b => b.zone_id === selectedZone);
    }

    // Filter by time period
    const now = new Date();
    switch (filter) {
      case 'today':
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        filtered = filtered.filter(b => new Date(b.created_at) >= today);
        break;
      case 'week':
        const thisWeek = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        filtered = filtered.filter(b => new Date(b.created_at) >= thisWeek);
        break;
      case 'month':
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        filtered = filtered.filter(b => new Date(b.created_at) >= thisMonth);
        break;
    }

    return filtered;
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getSeverityClass = (protectionLevel: string) => {
    switch (protectionLevel) {
      case 'no_take':
        return 'severity-high';
      case 'restricted':
        return 'severity-medium';
      case 'seasonal':
        return 'severity-low';
      default:
        return 'severity-medium';
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const filteredBreaches = getFilteredBreaches();

  return (
    <div className="breach-monitor">
      <div className="breach-monitor-header">
        <h2>Breach Monitoring</h2>
        <button 
          className="btn btn-secondary"
          onClick={loadData}
          disabled={isLoading}
        >
          Refresh
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Total Breaches</h3>
          <span className="stat-number">{stats.total}</span>
        </div>
        <div className="stat-card">
          <h3>Today</h3>
          <span className="stat-number">{stats.today}</span>
        </div>
        <div className="stat-card">
          <h3>This Week</h3>
          <span className="stat-number">{stats.thisWeek}</span>
        </div>
        <div className="stat-card">
          <h3>This Month</h3>
          <span className="stat-number">{stats.thisMonth}</span>
        </div>
      </div>

      {/* Filters */}
      <div className="filters">
        <div className="filter-group">
          <label>Time Period:</label>
          <select 
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Zone:</label>
          <select 
            value={selectedZone}
            onChange={(e) => setSelectedZone(e.target.value)}
          >
            <option value="all">All Zones</option>
            {zones.map(zone => (
              <option key={zone.id} value={zone.id}>
                {zone.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Breach Events List */}
      <div className="breach-list">
        <h3>Recent Breaches ({filteredBreaches.length})</h3>
        
        {filteredBreaches.length === 0 ? (
          <div className="empty-state">
            <p>No breach events found for the selected filters.</p>
          </div>
        ) : (
          <div className="breach-events">
            {filteredBreaches.map(breach => (
              <div 
                key={breach.id} 
                className={`breach-item ${getSeverityClass(breach.protected_zones?.protection_level || 'restricted')}`}
              >
                <div className="breach-header">
                  <h4>{breach.protected_zones?.name || 'Unknown Zone'}</h4>
                  <span className="breach-time">
                    {formatDateTime(breach.created_at)}
                  </span>
                </div>
                
                <div className="breach-details">
                  <div className="breach-meta">
                    <span className="protection-level">
                      {breach.protected_zones?.protection_level?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
                    </span>
                    {breach.user_profiles?.name && (
                      <span className="user-info">
                        User: {breach.user_profiles.name}
                      </span>
                    )}
                  </div>
                  
                  <div className="location-info">
                    <span>
                      Location: {breach.location.coordinates[1].toFixed(6)}, {breach.location.coordinates[0].toFixed(6)}
                    </span>
                  </div>

                  {breach.notes && (
                    <div className="breach-notes">
                      <strong>Notes:</strong> {breach.notes}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BreachMonitor;
export { BreachMonitor };

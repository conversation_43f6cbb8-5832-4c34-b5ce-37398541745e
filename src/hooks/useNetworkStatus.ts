import { useState, useEffect, useCallback } from 'react';

interface NetworkInfo {
  isOnline: boolean;
  connectionType: string | null;
  downlink: number | null;
  effectiveType: string | null;
  saveData: boolean;
}

interface UseNetworkStatusReturn extends NetworkInfo {
  wasOffline: boolean;
  isSlowConnection: boolean;
  checkConnectivity: () => Promise<boolean>;
}

/**
 * Custom hook for monitoring network status and connection quality
 */
export const useNetworkStatus = (): UseNetworkStatusReturn => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);
  const [connectionInfo, setConnectionInfo] = useState<Omit<NetworkInfo, 'isOnline'>>({
    connectionType: null,
    downlink: null,
    effectiveType: null,
    saveData: false
  });

  // Check if connection is slow based on effective type
  const isSlowConnection = connectionInfo.effectiveType === 'slow-2g' || 
                          connectionInfo.effectiveType === '2g' ||
                          (connectionInfo.downlink !== null && connectionInfo.downlink < 1);

  // Update connection info if Network Information API is available
  const updateConnectionInfo = useCallback(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;

      if (connection) {
        setConnectionInfo({
          connectionType: connection.type || null,
          downlink: connection.downlink || null,
          effectiveType: connection.effectiveType || null,
          saveData: connection.saveData || false
        });
      }
    }
  }, []);

  // Actively check connectivity by attempting to fetch a small resource
  const checkConnectivity = useCallback(async (): Promise<boolean> => {
    try {
      // Try to fetch from your own Supabase instance first (most reliable for your app)
      await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/`, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
        }
      });
      return true;
    } catch (error) {
      // Try alternative reliable endpoints
      try {
        await fetch('https://www.google.com/favicon.ico', {
          method: 'HEAD',
          mode: 'no-cors',
          cache: 'no-cache'
        });
        return true;
      } catch (secondError) {
        return false;
      }
    }
  }, []);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        setWasOffline(false);
        // Verify actual connectivity
        checkConnectivity().then(isActuallyOnline => {
          if (!isActuallyOnline) {
            setIsOnline(false);
          }
        });
      }
      updateConnectionInfo();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    const handleConnectionChange = () => {
      updateConnectionInfo();
    };

    // Listen for online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for connection changes (if supported)
    if ('connection' in navigator) {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;

      if (connection) {
        connection.addEventListener('change', handleConnectionChange);
      }
    }

    // Initial connection info update
    updateConnectionInfo();

    // Periodic connectivity check (every 30 seconds when online)
    const connectivityInterval = setInterval(async () => {
      if (navigator.onLine) {
        const actuallyOnline = await checkConnectivity();
        if (actuallyOnline !== isOnline) {
          setIsOnline(actuallyOnline);
          if (!actuallyOnline) {
            setWasOffline(true);
          }
        }
      }
    }, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection || 
                          (navigator as any).mozConnection || 
                          (navigator as any).webkitConnection;

        if (connection) {
          connection.removeEventListener('change', handleConnectionChange);
        }
      }

      clearInterval(connectivityInterval);
    };
  }, [isOnline, wasOffline, checkConnectivity, updateConnectionInfo]);

  return {
    isOnline,
    wasOffline,
    isSlowConnection,
    checkConnectivity,
    ...connectionInfo
  };
};

/**
 * Hook specifically for PWA offline capabilities
 */
export const useOfflineCapabilities = () => {
  const [hasServiceWorker, setHasServiceWorker] = useState(false);
  const [cacheInfo, setCacheInfo] = useState<{
    hasCache: boolean;
    cacheSize: number | null;
  }>({
    hasCache: false,
    cacheSize: null
  });

  useEffect(() => {
    // Check if service worker is registered
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        setHasServiceWorker(true);
      });
    }

    // Check cache availability and size
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        setCacheInfo(prev => ({
          ...prev,
          hasCache: cacheNames.length > 0
        }));

        // Estimate cache size (rough calculation)
        Promise.all(
          cacheNames.map(name => 
            caches.open(name).then(cache => 
              cache.keys().then(keys => keys.length)
            )
          )
        ).then(sizes => {
          const totalItems = sizes.reduce((sum, size) => sum + size, 0);
          setCacheInfo(prev => ({
            ...prev,
            cacheSize: totalItems
          }));
        });
      });
    }
  }, []);

  return {
    hasServiceWorker,
    hasCache: cacheInfo.hasCache,
    cacheSize: cacheInfo.cacheSize,
    isOfflineCapable: hasServiceWorker && cacheInfo.hasCache
  };
};

/**
 * Push Notification Service
 * Handles Web Push notifications for breach alerts and real-time updates
 */

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: NotificationAction[];
  requireInteraction?: boolean;
  silent?: boolean;
  tag?: string;
  timestamp?: number;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userId?: string;
  deviceId: string;
  userAgent: string;
  subscriptionTime: string;
}

export class PushNotificationService {
  private static readonly VAPID_PUBLIC_KEY = import.meta.env.VITE_VAPID_PUBLIC_KEY;
  private static readonly API_BASE_URL = import.meta.env.VITE_SUPABASE_URL;
  
  private static serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private static pushSubscription: PushSubscription | null = null;
  private static isSupported = false;

  /**
   * Initialize push notification service
   */
  static async initialize(): Promise<boolean> {
    try {
      // Check for service worker and push notification support
      if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
        console.warn('Push notifications not supported in this browser');
        return false;
      }

      // Check for Notification API support
      if (!('Notification' in window)) {
        console.warn('Notification API not supported');
        return false;
      }

      this.isSupported = true;

      // Get service worker registration
      this.serviceWorkerRegistration = await navigator.serviceWorker.ready;

      // Check existing subscription
      this.pushSubscription = await this.serviceWorkerRegistration.pushManager.getSubscription();

      console.log('Push notification service initialized');
      return true;

    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      return false;
    }
  }

  /**
   * Check if notifications are enabled
   */
  static isNotificationEnabled(): boolean {
    return 'Notification' in window && Notification.permission === 'granted';
  }

  /**
   * Get current notification permission status
   */
  static getNotificationPermission(): NotificationPermission {
    return 'Notification' in window ? Notification.permission : 'denied';
  }

  /**
   * Request notification permission and subscribe to push notifications
   */
  static async requestPermissionAndSubscribe(): Promise<PushSubscriptionData | null> {
    if (!this.isSupported || !this.serviceWorkerRegistration) {
      throw new Error('Push notifications not supported or not initialized');
    }

    if (!this.VAPID_PUBLIC_KEY) {
      throw new Error('VAPID public key not configured');
    }

    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      
      if (permission !== 'granted') {
        console.warn('Notification permission denied');
        return null;
      }

      // Subscribe to push notifications
      const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.VAPID_PUBLIC_KEY)
      });

      this.pushSubscription = subscription;

      // Create subscription data object
      const subscriptionData: PushSubscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!)
        },
        deviceId: this.getDeviceId(),
        userAgent: navigator.userAgent,
        subscriptionTime: new Date().toISOString()
      };

      // Send subscription to server
      await this.sendSubscriptionToServer(subscriptionData);

      console.log('Successfully subscribed to push notifications');
      return subscriptionData;

    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  static async unsubscribe(): Promise<boolean> {
    try {
      if (this.pushSubscription) {
        const success = await this.pushSubscription.unsubscribe();
        
        if (success) {
          // Remove subscription from server
          await this.removeSubscriptionFromServer();
          this.pushSubscription = null;
          console.log('Successfully unsubscribed from push notifications');
        }

        return success;
      }

      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  /**
   * Check if currently subscribed to push notifications
   */
  static isSubscribed(): boolean {
    return this.pushSubscription !== null;
  }

  /**
   * Get current push subscription
   */
  static getSubscription(): PushSubscription | null {
    return this.pushSubscription;
  }

  /**
   * Check notification permission status
   */
  static getPermissionStatus(): NotificationPermission {
    return 'Notification' in window ? Notification.permission : 'denied';
  }

  /**
   * Show a local notification (non-push)
   */
  static async showLocalNotification(payload: NotificationPayload): Promise<void> {
    if (!this.isSupported || Notification.permission !== 'granted') {
      console.warn('Cannot show notification: permission not granted');
      return;
    }

    try {
      const notification = new Notification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/pwa-192x192.png',
        badge: payload.badge || '/pwa-72x72.png',
        data: payload.data,
        requireInteraction: payload.requireInteraction || false,
        silent: payload.silent || false,
        tag: payload.tag
      });

      // Handle notification click
      notification.onclick = () => {
        notification.close();
        
        // Focus or open the app window
        if (payload.data?.url) {
          window.open(payload.data.url, '_blank');
        } else {
          window.focus();
        }
      };

      // Auto-close after 10 seconds unless requireInteraction is true
      if (!payload.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 10000);
      }

    } catch (error) {
      console.error('Failed to show local notification:', error);
    }
  }

  /**
   * Send a test notification
   */
  static async sendTestNotification(): Promise<void> {
    if (!this.pushSubscription) {
      throw new Error('Not subscribed to push notifications');
    }

    try {
      const testPayload = {
        title: 'Test Notification',
        body: 'This is a test notification from Coastal Marine Protection',
        type: 'test',
        timestamp: new Date().toISOString()
      };

      await this.sendNotificationToServer(testPayload);
      console.log('Test notification sent');

    } catch (error) {
      console.error('Failed to send test notification:', error);
      throw error;
    }
  }

  /**
   * Register notification click handlers for different action types
   */
  static registerNotificationHandlers(): void {
    if (!this.serviceWorkerRegistration) return;

    // Listen for notification click events from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'notification-click') {
        this.handleNotificationClick(event.data.payload);
      }
    });
  }

  /**
   * Handle notification click actions
   */
  private static handleNotificationClick(data: any): void {
    console.log('Notification clicked:', data);

    // Handle different notification types
    switch (data.action) {
      case 'view-breach':
        // Navigate to breach details
        window.location.href = `/breach/${data.breachId}`;
        break;
      
      case 'view-zone':
        // Navigate to zone details
        window.location.href = `/zones/${data.zoneId}`;
        break;
      
      case 'open-app':
      default:
        // Just open/focus the app
        window.focus();
        break;
    }
  }

  /**
   * Convert VAPID public key to Uint8Array
   */
  private static urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }

    return outputArray;
  }

  /**
   * Convert ArrayBuffer to base64
   */
  private static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    bytes.forEach(byte => binary += String.fromCharCode(byte));
    return window.btoa(binary);
  }

  /**
   * Get or generate device ID
   */
  private static getDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }

  /**
   * Send subscription data to server
   */
  private static async sendSubscriptionToServer(subscription: PushSubscriptionData): Promise<void> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/rest/v1/push_subscriptions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`
        },
        body: JSON.stringify(subscription)
      });

      if (!response.ok) {
        throw new Error(`Failed to save subscription: ${response.statusText}`);
      }

      console.log('Subscription saved to server');
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
      // Don't throw - subscription can still work locally
    }
  }

  /**
   * Remove subscription from server
   */
  private static async removeSubscriptionFromServer(): Promise<void> {
    try {
      const deviceId = this.getDeviceId();
      
      const response = await fetch(`${this.API_BASE_URL}/rest/v1/push_subscriptions?device_id=eq.${deviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to remove subscription: ${response.statusText}`);
      }

      console.log('Subscription removed from server');
    } catch (error) {
      console.error('Failed to remove subscription from server:', error);
    }
  }

  /**
   * Send notification payload to server for delivery
   */
  private static async sendNotificationToServer(payload: any): Promise<void> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/functions/v1/send-push-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`
        },
        body: JSON.stringify({
          deviceId: this.getDeviceId(),
          payload
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send notification: ${response.statusText}`);
      }

    } catch (error) {
      console.error('Failed to send notification to server:', error);
      throw error;
    }
  }

  /**
   * Configure notification preferences
   */
  static async updateNotificationPreferences(preferences: {
    breachAlerts: boolean;
    zoneUpdates: boolean;
    systemMessages: boolean;
    quietHours?: {
      enabled: boolean;
      startTime: string;
      endTime: string;
    };
  }): Promise<void> {
    try {
      localStorage.setItem('notificationPreferences', JSON.stringify(preferences));
      
      // Send preferences to server
      await fetch(`${this.API_BASE_URL}/rest/v1/user_notification_preferences`, {
        method: 'UPSERT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`
        },
        body: JSON.stringify({
          device_id: this.getDeviceId(),
          preferences
        })
      });

      console.log('Notification preferences updated');
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
    }
  }

  /**
   * Get current notification preferences
   */
  static getNotificationPreferences(): any {
    try {
      const stored = localStorage.getItem('notificationPreferences');
      return stored ? JSON.parse(stored) : {
        breachAlerts: true,
        zoneUpdates: true,
        systemMessages: true,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '07:00'
        }
      };
    } catch {
      return {
        breachAlerts: true,
        zoneUpdates: true,
        systemMessages: true
      };
    }
  }

  /**
   * Check if notifications are supported
   */
  static isNotificationSupported(): boolean {
    return this.isSupported;
  }

  /**
   * Get notification statistics
   */
  static getNotificationStats(): {
    isSupported: boolean;
    permission: NotificationPermission;
    isSubscribed: boolean;
    subscriptionEndpoint?: string;
  } {
    return {
      isSupported: this.isSupported,
      permission: this.getPermissionStatus(),
      isSubscribed: this.isSubscribed(),
      subscriptionEndpoint: this.pushSubscription?.endpoint
    };
  }
}

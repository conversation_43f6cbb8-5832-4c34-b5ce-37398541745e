-- Coastal Marine Protection Database Schema - Development Version (No RLS)
-- Run this in your Supabase SQL editor for development/testing

-- Enable PostGIS extension for geospatial data
CREATE EXTENSION IF NOT EXISTS postgis;

-- Create custom types (with IF NOT EXISTS equivalent)
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('authority', 'community');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE zone_status AS ENUM ('active', 'inactive', 'draft');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE breach_severity AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  name TEXT NOT NULL,
  role user_role NOT NULL DEFAULT 'community',
  organization TEXT,
  phone TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Protected zones table
CREATE TABLE IF NOT EXISTS protected_zones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  geometry GEOMETRY(POLYGON, 4326) NOT NULL,
  properties JSONB DEFAULT '{}',
  status zone_status DEFAULT 'active',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Breach events table
CREATE TABLE IF NOT EXISTS breach_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  zone_id UUID REFERENCES protected_zones(id),
  user_id UUID REFERENCES auth.users(id),
  location GEOMETRY(POINT, 4326) NOT NULL,
  severity breach_severity DEFAULT 'medium',
  acknowledged BOOLEAN DEFAULT FALSE,
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Download regions table (for offline map data)
CREATE TABLE IF NOT EXISTS download_regions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  bounds GEOMETRY(POLYGON, 4326) NOT NULL,
  zoom_levels INTEGER[] DEFAULT ARRAY[10,11,12,13,14,15],
  size_mb DECIMAL(10,2),
  downloaded_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance (DROP IF EXISTS first)
DROP INDEX IF EXISTS idx_protected_zones_geometry;
CREATE INDEX idx_protected_zones_geometry ON protected_zones USING GIST (geometry);

DROP INDEX IF EXISTS idx_breach_events_location;
CREATE INDEX idx_breach_events_location ON breach_events USING GIST (location);

DROP INDEX IF EXISTS idx_breach_events_zone_id;
CREATE INDEX idx_breach_events_zone_id ON breach_events (zone_id);

DROP INDEX IF EXISTS idx_breach_events_created_at;
CREATE INDEX idx_breach_events_created_at ON breach_events (created_at);

DROP INDEX IF EXISTS idx_download_regions_bounds;
CREATE INDEX idx_download_regions_bounds ON download_regions USING GIST (bounds);

DROP INDEX IF EXISTS idx_download_regions_user_id;
CREATE INDEX idx_download_regions_user_id ON download_regions (user_id);

-- DISABLE Row Level Security for development
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE protected_zones DISABLE ROW LEVEL SECURITY;
ALTER TABLE breach_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE download_regions DISABLE ROW LEVEL SECURITY;

-- Functions for common operations

-- Function to check if a point is within any protected zone
CREATE OR REPLACE FUNCTION check_point_in_zones(lat DECIMAL, lng DECIMAL)
RETURNS TABLE(zone_id UUID, zone_name TEXT, severity breach_severity) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pz.id,
    pz.name,
    CASE 
      WHEN pz.properties->>'severity' = 'critical' THEN 'critical'::breach_severity
      WHEN pz.properties->>'severity' = 'high' THEN 'high'::breach_severity
      WHEN pz.properties->>'severity' = 'medium' THEN 'medium'::breach_severity
      ELSE 'low'::breach_severity
    END
  FROM protected_zones pz
  WHERE ST_Contains(pz.geometry, ST_SetSRID(ST_MakePoint(lng, lat), 4326))
    AND pz.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a breach event
CREATE OR REPLACE FUNCTION create_breach_event(
  p_zone_id UUID,
  p_user_id UUID,
  p_lat DECIMAL,
  p_lng DECIMAL,
  p_severity breach_severity DEFAULT 'medium',
  p_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
  breach_id UUID;
BEGIN
  INSERT INTO breach_events (zone_id, user_id, location, severity, metadata)
  VALUES (
    p_zone_id,
    p_user_id,
    ST_SetSRID(ST_MakePoint(p_lng, p_lat), 4326),
    p_severity,
    p_metadata
  )
  RETURNING id INTO breach_id;
  
  RETURN breach_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers first
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
DROP TRIGGER IF EXISTS update_protected_zones_updated_at ON protected_zones;

-- Create triggers
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_protected_zones_updated_at
  BEFORE UPDATE ON protected_zones
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO protected_zones (name, description, geometry, properties)
VALUES (
  'San Vicente Marine Reserve',
  'Protected marine area around San Vicente coast',
  ST_GeomFromText('POLYGON((-70.123 -33.456, -70.120 -33.456, -70.120 -33.459, -70.123 -33.459, -70.123 -33.456))', 4326),
  '{"severity": "high", "restrictions": ["fishing", "swimming", "boating"]}'
) ON CONFLICT DO NOTHING;

-- Add more sample zones around different coastal areas
INSERT INTO protected_zones (name, description, geometry, properties)
VALUES 
  (
    'Valparaiso Marine Sanctuary',
    'Critical breeding area for marine mammals',
    ST_GeomFromText('POLYGON((-71.6 -33.0, -71.5 -33.0, -71.5 -33.1, -71.6 -33.1, -71.6 -33.0))', 4326),
    '{"severity": "critical", "restrictions": ["all_activities"]}'
  ),
  (
    'Coastal Conservation Zone',
    'General protection area for coastal ecosystem',
    ST_GeomFromText('POLYGON((-71.2 -32.8, -71.1 -32.8, -71.1 -32.9, -71.2 -32.9, -71.2 -32.8))', 4326),
    '{"severity": "medium", "restrictions": ["commercial_fishing", "motorized_boats"]}'
  )
ON CONFLICT DO NOTHING;

-- Verification queries
SELECT 'Database setup completed successfully' as status;
SELECT 
  table_name,
  CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as rls_status
FROM information_schema.tables t
JOIN pg_class c ON c.relname = t.table_name
WHERE t.table_schema = 'public' 
AND t.table_name IN ('user_profiles', 'protected_zones', 'breach_events', 'download_regions');

SELECT count(*) as total_zones FROM protected_zones;

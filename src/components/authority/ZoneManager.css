.zone-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.zone-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.zone-manager-header h2 {
  margin: 0;
  color: var(--dark-gray);
  font-size: 1.25rem;
}

.zone-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.zone-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background-color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin: 0;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: var(--spacing-xs) 0 0 0;
}

.zones-list {
  flex: 1;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.zones-list-header {
  background-color: var(--light-gray);
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zones-list-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-gray);
}

.search-input {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.zones-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.zones-table th,
.zones-table td {
  text-align: left;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
}

.zones-table th {
  background-color: var(--light-gray);
  font-weight: 600;
  color: var(--dark-gray);
}

.zones-table tbody tr:hover {
  background-color: var(--light-gray);
}

.zone-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.zone-status.active {
  background-color: #d4edda;
  color: #155724;
}

.zone-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.zone-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-button {
  padding: var(--spacing-xs);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  transform: scale(1.1);
}

.edit-button {
  background-color: var(--primary-blue);
  color: var(--white);
}

.delete-button {
  background-color: var(--danger-red);
  color: var(--white);
}

.view-button {
  background-color: var(--success-green);
  color: var(--white);
}

.zone-form {
  background-color: var(--white);
  padding: var(--spacing-lg);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.zone-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.zone-form-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
}

.zone-form-header h3 {
  margin: 0;
  color: var(--dark-gray);
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--medium-gray);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background-color: var(--light-gray);
  color: var(--dark-gray);
}

.form-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid #e9ecef;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--medium-gray);
}

.empty-state h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--medium-gray);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .zone-manager {
    padding: var(--spacing-sm);
  }
  
  .zone-manager-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .zone-controls {
    justify-content: center;
  }
  
  .zone-stats {
    grid-template-columns: 1fr;
  }
  
  .zones-list-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .zones-table {
    font-size: 12px;
  }
  
  .zones-table th,
  .zones-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

// Debug utility for authentication issues
import { supabase } from '../services/supabase';

export const debugAuthState = async () => {
  console.log('=== AUTH DEBUG START ===');
  
  try {
    // Check session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    console.log('Session check:', {
      hasSession: !!sessionData.session,
      hasUser: !!sessionData.session?.user,
      userId: sessionData.session?.user?.id,
      error: sessionError
    });

    if (sessionData.session?.user) {
      // Check user auth data
      const { data: userData, error: userError } = await supabase.auth.getUser();
      console.log('User check:', {
        hasUser: !!userData.user,
        email: userData.user?.email,
        error: userError
      });
    }

    // Check localStorage for any cached auth data
    const authData = localStorage.getItem('sb-vlnnomecuuhzjpdnamyw-auth-token');
    console.log('LocalStorage auth data exists:', !!authData);

  } catch (error) {
    console.error('Debug auth error:', error);
  }
  
  console.log('=== AUTH DEBUG END ===');
};

// Call this on page load for debugging
if (typeof window !== 'undefined') {
  window.debugAuth = debugAuthState;
}

{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "moduleDetection": "force", "noEmit": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "types": ["workbox-sw"]}, "include": ["src/sw.ts", "src/types/sw.d.ts"]}
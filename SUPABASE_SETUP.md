# Supabase Configuration Instructions

## Current Issue
The Supabase URL in your .env file is returning a 404 error, indicating the project doesn't exist or has been deleted.

## To Fix This:

1. **Create a new Supabase project:**
   - Go to https://supabase.com
   - Sign in or create an account
   - Click "New Project"
   - Choose an organization and enter project details

2. **Get your credentials:**
   - Once the project is created, go to Settings > API
   - Copy the "Project URL" and "anon/public" key

3. **Update your .env file:**
   ```
   VITE_SUPABASE_URL=https://your-new-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-new-anon-key
   ```

4. **Set up the database:**
   - Run the SQL commands from database/schema.sql in your Supabase SQL editor
   - This will create the necessary tables and Row Level Security policies

## Temporary Testing
For now, the app includes better error handling that will show a connection error message instead of infinite loading.

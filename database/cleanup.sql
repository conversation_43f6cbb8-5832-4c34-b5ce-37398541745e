-- Cleanup script to remove existing objects
-- Run this BEFORE running the main schema if you want to start fresh

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS download_regions CASCADE;
DROP TABLE IF EXISTS breach_events CASCADE;
DROP TABLE IF EXISTS protected_zones CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS breach_severity CASCADE;
DROP TYPE IF EXISTS zone_status CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS check_point_in_zones(DECIMAL, DECIMAL) CASCADE;
DROP FUNCTION IF EXISTS create_breach_event(U<PERSON><PERSON>, U<PERSON>D, DECIMA<PERSON>, DECIMAL, breach_severity, JSONB) CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

/**
 * Background Location Service
 * Handles continuous location tracking, background sync, and service worker integration
 */

import type { ProtectedZone } from '../types';
import { GeofencingService } from './geofencing';
import { OfflineStorageService } from './offlineStorage';

export interface LocationUpdate {
  id: string;
  coordinates: [number, number];
  timestamp: string;
  accuracy: number;
  heading?: number;
  speed?: number;
  altitude?: number;
  deviceId: string;
}

export interface BackgroundLocationOptions {
  enableHighAccuracy: boolean;
  timeout: number;
  maximumAge: number;
  distanceFilter: number; // Minimum distance in meters before triggering update
  interval: number; // Update interval in milliseconds
  backgroundMode: boolean;
}

export class BackgroundLocationService {
  private static isTracking = false;
  private static watchId: number | null = null;
  private static lastLocation: [number, number] | null = null;
  private static lastUpdateTime = 0;
  private static options: BackgroundLocationOptions = {
    enableHighAccuracy: true,
    timeout: 15000,
    maximumAge: 10000,
    distanceFilter: 5, // 5 meters
    interval: 10000, // 10 seconds
    backgroundMode: false
  };

  private static onLocationCallback?: (location: LocationUpdate) => void;
  private static onGeofenceCallback?: (zone: ProtectedZone, coords: [number, number]) => void;
  private static onErrorCallback?: (error: GeolocationPositionError) => void;

  /**
   * Initialize background location tracking
   */
  static async startBackgroundTracking(options?: Partial<BackgroundLocationOptions>): Promise<boolean> {
    if (this.isTracking) {
      console.log('Background location tracking already active');
      return true;
    }

    // Check for geolocation support
    if (!navigator.geolocation) {
      console.error('Geolocation not supported');
      return false;
    }

    // Merge options
    this.options = { ...this.options, ...options };

    // Request persistent notification permission for background tracking
    if (this.options.backgroundMode && 'Notification' in window) {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        console.warn('Notification permission denied - background tracking may be limited');
      }
    }

    // Start location watching
    const geolocationOptions: PositionOptions = {
      enableHighAccuracy: this.options.enableHighAccuracy,
      timeout: this.options.timeout,
      maximumAge: this.options.maximumAge
    };

    this.watchId = navigator.geolocation.watchPosition(
      this.handleLocationUpdate.bind(this),
      this.handleLocationError.bind(this),
      geolocationOptions
    );

    this.isTracking = true;

    // Register service worker for background processing
    if (this.options.backgroundMode) {
      await this.registerBackgroundSync();
    }

    console.log('Background location tracking started');
    return true;
  }

  /**
   * Stop background location tracking
   */
  static stopBackgroundTracking(): void {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }

    this.isTracking = false;
    this.lastLocation = null;
    this.lastUpdateTime = 0;

    console.log('Background location tracking stopped');
  }

  /**
   * Check if location tracking is currently active
   */
  static isBackgroundTracking(): boolean {
    return this.isTracking;
  }

  /**
   * Set callback for location updates
   */
  static setLocationCallback(callback: (location: LocationUpdate) => void): void {
    this.onLocationCallback = callback;
  }

  /**
   * Set callback for geofence alerts
   */
  static setGeofenceCallback(callback: (zone: ProtectedZone, coords: [number, number]) => void): void {
    this.onGeofenceCallback = callback;
  }

  /**
   * Set callback for location errors
   */
  static setErrorCallback(callback: (error: GeolocationPositionError) => void): void {
    this.onErrorCallback = callback;
  }

  /**
   * Handle location updates from geolocation API
   */
  private static async handleLocationUpdate(position: GeolocationPosition): Promise<void> {
    const coords: [number, number] = [position.coords.longitude, position.coords.latitude];
    const timestamp = new Date().toISOString();
    const now = Date.now();

    // Apply distance filter to reduce unnecessary updates
    if (this.lastLocation && this.shouldFilterLocation(coords, this.lastLocation)) {
      return;
    }

    // Apply time filter
    if (now - this.lastUpdateTime < this.options.interval) {
      return;
    }

    // Create location update object
    const locationUpdate: LocationUpdate = {
      id: `loc_${now}_${Math.random().toString(36).substr(2, 9)}`,
      coordinates: coords,
      timestamp,
      accuracy: position.coords.accuracy,
      heading: position.coords.heading || undefined,
      speed: position.coords.speed || undefined,
      altitude: position.coords.altitude || undefined,
      deviceId: this.getDeviceId()
    };

    // Store location update for offline sync
    await this.storeLocationUpdate(locationUpdate);

    // Update tracking state
    this.lastLocation = coords;
    this.lastUpdateTime = now;

    // Trigger callbacks
    if (this.onLocationCallback) {
      this.onLocationCallback(locationUpdate);
    }

    // Check for geofence violations
    await this.checkGeofenceViolations(coords);

    // Send to service worker for background processing
    if (this.options.backgroundMode && 'serviceWorker' in navigator) {
      navigator.serviceWorker.controller?.postMessage({
        type: 'LOCATION_UPDATE',
        payload: { coords: position.coords }
      });
    }
  }

  /**
   * Handle location errors
   */
  private static handleLocationError(error: GeolocationPositionError): void {
    console.error('Background location error:', error.message);

    if (this.onErrorCallback) {
      this.onErrorCallback(error);
    }

    // Show user-friendly error message
    const errorMessages = {
      [error.PERMISSION_DENIED]: 'Location access denied. Please enable location permissions.',
      [error.POSITION_UNAVAILABLE]: 'Location information is unavailable.',
      [error.TIMEOUT]: 'Location request timed out. Retrying...'
    };

    const message = errorMessages[error.code as keyof typeof errorMessages] || 'Unknown location error occurred.';
    console.log('Location error:', message);
  }

  /**
   * Check if location should be filtered based on distance
   */
  private static shouldFilterLocation(newCoords: [number, number], lastCoords: [number, number]): boolean {
    const distance = this.calculateDistance(newCoords, lastCoords);
    return distance < this.options.distanceFilter;
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private static calculateDistance(coords1: [number, number], coords2: [number, number]): number {
    const [lon1, lat1] = coords1;
    const [lon2, lat2] = coords2;
    
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * Store location update for offline sync
   */
  private static async storeLocationUpdate(update: LocationUpdate): Promise<void> {
    try {
      // Store in IndexedDB for offline access
      const updates = await this.getStoredLocationUpdates();
      updates.push(update);
      
      // Keep only last 1000 updates to manage storage
      if (updates.length > 1000) {
        updates.splice(0, updates.length - 1000);
      }

      await OfflineStorageService.storeLocationUpdates(updates);
    } catch (error) {
      console.error('Error storing location update:', error);
    }
  }

  /**
   * Get stored location updates
   */
  private static async getStoredLocationUpdates(): Promise<LocationUpdate[]> {
    try {
      return await OfflineStorageService.getLocationUpdates() || [];
    } catch (error) {
      console.error('Error getting stored location updates:', error);
      return [];
    }
  }

  /**
   * Check for geofence violations
   */
  private static async checkGeofenceViolations(coords: [number, number]): Promise<void> {
    try {
      const violatedZones = await GeofencingService.checkLocationInZones(coords);
      
      for (const zone of violatedZones) {
        if (this.onGeofenceCallback) {
          this.onGeofenceCallback(zone, coords);
        }

        // Create system notification for background alerts
        if (this.options.backgroundMode && 'Notification' in window && Notification.permission === 'granted') {
          new Notification('Protected Zone Alert', {
            body: `You have entered: ${zone.properties?.name || zone.name}`,
            icon: '/pwa-192x192.png',
            badge: '/pwa-72x72.png',
            tag: `zone-${zone.id}`,
            requireInteraction: true,
            silent: false
          });
        }
      }
    } catch (error) {
      console.error('Error checking geofence violations:', error);
    }
  }

  /**
   * Register service worker for background sync
   */
  private static async registerBackgroundSync(): Promise<void> {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('background-location-sync');
        console.log('Background sync registered for location updates');
      } catch (error) {
        console.error('Failed to register background sync:', error);
      }
    }
  }

  /**
   * Get or generate device ID
   */
  private static getDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }

  /**
   * Get current tracking options
   */
  static getTrackingOptions(): BackgroundLocationOptions {
    return { ...this.options };
  }

  /**
   * Update tracking options (requires restart to take effect)
   */
  static updateTrackingOptions(newOptions: Partial<BackgroundLocationOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * Get location history for a specific time range
   */
  static async getLocationHistory(startTime: string, endTime: string): Promise<LocationUpdate[]> {
    try {
      const allUpdates = await this.getStoredLocationUpdates();
      return allUpdates.filter(update => 
        update.timestamp >= startTime && update.timestamp <= endTime
      );
    } catch (error) {
      console.error('Error getting location history:', error);
      return [];
    }
  }

  /**
   * Clear location history
   */
  static async clearLocationHistory(): Promise<void> {
    try {
      await OfflineStorageService.clearLocationUpdates();
      console.log('Location history cleared');
    } catch (error) {
      console.error('Error clearing location history:', error);
    }
  }

  /**
   * Export location data for backup or analysis
   */
  static async exportLocationData(): Promise<string> {
    try {
      const updates = await this.getStoredLocationUpdates();
      return JSON.stringify({
        exportDate: new Date().toISOString(),
        deviceId: this.getDeviceId(),
        totalUpdates: updates.length,
        updates
      }, null, 2);
    } catch (error) {
      console.error('Error exporting location data:', error);
      return '{}';
    }
  }

  /**
   * Get last update timestamp
   */
  static getLastUpdateTime(): string | undefined {
    return this.lastUpdateTime > 0 ? new Date(this.lastUpdateTime).toISOString() : undefined;
  }

  /**
   * Get current tracking status
   */
  static getTrackingStatus(): {
    isTracking: boolean;
    lastUpdate?: string;
    options: BackgroundLocationOptions;
  } {
    return {
      isTracking: this.isTracking,
      lastUpdate: this.getLastUpdateTime(),
      options: this.options
    };
  }
}

.mapbox-map {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.user-location-marker {
  font-size: 24px;
  cursor: pointer;
}

.map-info-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.location-active {
  color: #2e7d32;
  font-weight: 500;
}

.location-searching {
  color: #f57c00;
  font-weight: 500;
}

.location-inactive {
  color: #666;
}

.zones-info {
  color: #666;
}

.zone-info-popup {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  z-index: 1000;
}

.popup-content {
  padding: 1rem;
  position: relative;
}

.popup-content h3 {
  margin: 0 0 0.5rem 0;
  color: #1976d2;
  font-size: 1.125rem;
}

.popup-content p {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.875rem;
  line-height: 1.4;
}

.popup-content p:last-of-type {
  margin-bottom: 0;
}

.close-popup {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 1.125rem;
  cursor: pointer;
  color: #666;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-popup:hover {
  background: #f5f5f5;
}

/* Mapbox GL overrides */
.mapboxgl-ctrl-group {
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.mapboxgl-ctrl-top-left {
  top: 80px !important;
  left: 10px !important;
}

.mapboxgl-ctrl-top-right {
  top: 10px !important;
  right: 10px !important;
}

.mapboxgl-ctrl-bottom-right {
  bottom: 10px !important;
  right: 10px !important;
}

/* Draw controls styling */
.mapbox-gl-draw_ctrl-draw-btn {
  background-color: #1976d2 !important;
  color: white !important;
}

.mapbox-gl-draw_ctrl-draw-btn:hover {
  background-color: #1565c0 !important;
}

@media (max-width: 768px) {
  .map-info-panel {
    font-size: 0.75rem;
    padding: 8px;
    min-width: 160px;
  }

  .zone-info-popup {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .mapboxgl-ctrl-top-left {
    top: 120px !important;
  }
}

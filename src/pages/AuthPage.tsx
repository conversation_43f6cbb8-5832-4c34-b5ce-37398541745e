import React, { useState } from 'react';
import { useApp } from '../store/AppContext';
import './AuthPage.css';

export const AuthPage: React.FC = () => {
  const { signIn, signUp, state } = useApp();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<'authority' | 'community'>('community');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Clear local error when switching between login/signup
  const handleTabChange = (loginMode: boolean) => {
    setIsLogin(loginMode);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    console.log('AuthPage: Starting authentication...', { isLogin, email, role });

    try {
      if (isLogin) {
        console.log('AuthPage: Attempting sign in...');
        await signIn(email, password);
        console.log('AuthPage: Sign in completed successfully');
      } else {
        console.log('AuthPage: Attempting sign up...');
        await signUp(email, password, role);
        console.log('AuthPage: Sign up completed successfully');
      }
    } catch (err: any) {
      console.error('AuthPage: Authentication error:', err);
      setError(err.message || 'An error occurred');
    } finally {
      console.log('AuthPage: Setting loading to false');
      setLoading(false);
    }
  };

  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-header">
          <h1>🌊 Coastal Guard</h1>
          <p>Marine Protected Area Management</p>
        </div>

        <div className="auth-tabs">
          <button 
            className={isLogin ? 'active' : ''}
            onClick={() => handleTabChange(true)}
          >
            Sign In
          </button>
          <button 
            className={!isLogin ? 'active' : ''}
            onClick={() => handleTabChange(false)}
          >
            Sign Up
          </button>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          {/* Show global error from AppContext if present */}
          {state.error && <div className="error-message">{state.error}</div>}
          {/* Show local form error if present */}
          {error && <div className="error-message">{error}</div>}
          
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your password"
              minLength={6}
            />
          </div>

          {!isLogin && (
            <div className="form-group">
              <label htmlFor="role">Role</label>
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value as 'authority' | 'community')}
              >
                <option value="community">Community User</option>
                <option value="authority">Authority</option>
              </select>
              <small className="role-description">
                {role === 'community' 
                  ? 'For fishers, divers, and boat operators who need navigation alerts'
                  : 'For marine wardens and enforcement officers managing protected zones'
                }
              </small>
            </div>
          )}

          <button 
            type="submit" 
            className="auth-button"
            disabled={loading || state.isLoading}
          >
            {(loading || state.isLoading) ? 'Loading...' : (isLogin ? 'Sign In' : 'Create Account')}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            {isLogin ? "Don't have an account? " : "Already have an account? "}
            <button 
              type="button"
              onClick={() => handleTabChange(!isLogin)}
              className="link-button"
              disabled={loading || state.isLoading}
            >
              {isLogin ? 'Sign up here' : 'Sign in here'}
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

-- Disable Row Level Security - Temporary for Development
-- Run this in your Supabase SQL editor to disable <PERSON><PERSON> temporarily

-- Disable <PERSON><PERSON> on all tables
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE protected_zones DISABLE ROW LEVEL SECURITY;
ALTER TABLE breach_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE download_regions DISABLE ROW LEVEL SECURITY;

-- Drop all RLS policies to clean up
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;

DROP POLICY IF EXISTS "Everyone can view active zones" ON protected_zones;
DROP POLICY IF EXISTS "Authorities can manage zones" ON protected_zones;

DROP POLICY IF EXISTS "Authorities can view all breach events" ON breach_events;
DROP POLICY IF EXISTS "Users can view their own breach events" ON breach_events;
DROP POLICY IF EXISTS "Community users can insert breach events" ON breach_events;
DROP POLICY IF EXISTS "Authorities can update breach events" ON breach_events;

DROP POLICY IF EXISTS "Users can manage their own download regions" ON download_regions;

-- Verification
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('user_profiles', 'protected_zones', 'breach_events', 'download_regions');

-- This should show 'f' (false) for rowsecurity column for all tables
SELECT 'RLS disabled successfully' as status;

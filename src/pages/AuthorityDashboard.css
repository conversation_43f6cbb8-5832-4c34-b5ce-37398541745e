.authority-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left h1 {
  margin: 0 0 0.25rem 0;
  color: #1976d2;
  font-size: 1.5rem;
}

.user-info {
  color: #666;
  font-size: 0.875rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  background: #f5f5f5;
}

.status-indicator.online {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-indicator.offline {
  background: #ffebee;
  color: #c62828;
}

.sign-out-btn {
  padding: 0.5rem 1rem;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.sign-out-btn:hover {
  background: #d32f2f;
}

.dashboard-nav {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 2rem;
  display: flex;
  gap: 0;
}

.dashboard-nav button {
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
}

.dashboard-nav button:hover {
  background: #f5f5f5;
}

.dashboard-nav button.active {
  border-bottom-color: #1976d2;
  color: #1976d2;
  font-weight: 500;
}

.dashboard-content {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  overflow: hidden;
}

.map-tab,
.zones-tab,
.breaches-tab {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.map-tab {
  position: relative;
}

.zones-tab,
.breaches-tab {
  padding: 1.5rem;
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }

  .dashboard-nav {
    padding: 0 1rem;
    overflow-x: auto;
  }

  .dashboard-nav button {
    padding: 1rem;
    font-size: 0.875rem;
  }

  .dashboard-content {
    padding: 1rem;
  }
}

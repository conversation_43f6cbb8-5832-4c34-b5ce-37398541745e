import React, { useState, useEffect } from 'react';
import './NetworkStatus.css';

interface NetworkStatusProps {
  showWhenOnline?: boolean;
  className?: string;
  variant?: 'header' | 'standalone' | 'minimal';
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({ 
  showWhenOnline = true, 
  className = '',
  variant = 'standalone'
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);
  const [showReconnected, setShowReconnected] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        setShowReconnected(true);
        setWasOffline(false);
        // Hide reconnected message after 3 seconds
        setTimeout(() => setShowReconnected(false), 3000);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      setShowReconnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  if (isOnline && !showWhenOnline && !showReconnected) {
    return null;
  }

  const getStatusText = () => {
    if (showReconnected) return 'Back online';
    return isOnline ? 'Online' : 'Offline';
  };

  const getStatusIcon = () => {
    if (showReconnected) return '✅';
    return isOnline ? '🟢' : '🔴';
  };

  const getStatusClass = () => {
    if (showReconnected) return 'reconnected';
    return isOnline ? 'online' : 'offline';
  };

  return (
    <div className={`network-status ${variant} ${getStatusClass()} ${className}`}>
      {variant === 'minimal' ? (
        <span className="status-dot" title={getStatusText()} />
      ) : (
        <>
          <span className="status-icon">{getStatusIcon()}</span>
          <span className="status-text">{getStatusText()}</span>
          {!isOnline && variant === 'standalone' && (
            <span className="status-subtitle">Some features may be limited</span>
          )}
        </>
      )}
    </div>
  );
};

export default NetworkStatus;

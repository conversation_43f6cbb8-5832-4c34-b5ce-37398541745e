/* Network Status Styles */
.network-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* Variants */
.network-status.header {
  padding: 0.25rem 0.75rem;
  background: transparent;
}

.network-status.standalone {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--white);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.network-status.minimal {
  padding: 0;
  background: transparent;
}

/* Status States */
.network-status.online {
  color: var(--success-green);
}

.network-status.offline {
  color: var(--danger-red);
}

.network-status.reconnected {
  color: var(--success-green);
  animation: reconnectedPulse 0.5s ease-in-out;
}

.network-status.offline.standalone {
  background: #fef2f2;
  border-color: #fecaca;
}

.network-status.reconnected.standalone {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

/* Status Elements */
.status-icon {
  display: inline-block;
  font-size: 0.75rem;
}

.status-text {
  font-weight: 600;
}

.status-subtitle {
  font-size: 0.75rem;
  color: var(--medium-gray);
  font-weight: 400;
  margin-left: var(--spacing-xs);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  display: inline-block;
}

/* Animations */
@keyframes reconnectedPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Offline Banner */
.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--danger-red);
  color: var(--white);
  padding: var(--spacing-sm);
  text-align: center;
  z-index: 9999;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: slideDown 0.3s ease-out;
}

.offline-banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.offline-banner .status-icon {
  font-size: 1rem;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Online Banner (when reconnecting) */
.online-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--success-green);
  color: var(--white);
  padding: var(--spacing-sm);
  text-align: center;
  z-index: 9999;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: slideDown 0.3s ease-out, slideUp 0.3s ease-in 2.7s forwards;
}

@keyframes slideUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .network-status.standalone {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }

  .status-subtitle {
    display: none;
  }

  .offline-banner,
  .online-banner {
    font-size: 0.8rem;
    padding: var(--spacing-xs);
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .network-status.standalone {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .network-status.offline.standalone {
    background: #451a1a;
    border-color: #7f1d1d;
  }

  .network-status.reconnected.standalone {
    background: #14532d;
    border-color: #166534;
  }
}

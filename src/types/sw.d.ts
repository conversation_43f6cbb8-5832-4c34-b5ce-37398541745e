// Service Worker Type Definitions
declare var self: ServiceWorkerGlobalScope & {
  __WB_MANIFEST: any;
  skipWaiting(): Promise<void>;
  addEventListener: (type: string, listener: (event: any) => void) => void;
  registration: ServiceWorkerRegistration & {
    showNotification(title: string, options?: any): Promise<void>;
  };
};

interface ExtendedNotificationOptions {
  actions?: NotificationAction[];
  badge?: string;
  body?: string;
  data?: any;
  dir?: NotificationDirection;
  icon?: string;
  image?: string;
  lang?: string;
  renotify?: boolean;
  requireInteraction?: boolean;
  silent?: boolean;
  tag?: string;
  vibrate?: VibratePattern;
}

interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

import React, { useState, useEffect } from 'react';
import { useApp } from '../store/AppContext';
import type { ProtectedZone } from '../types';
import { OfflineStorageService } from '../services/offlineStorage';
import { GeofencingService } from '../services/geofencing';
import MapboxMap from '../components/map/MapboxMap';
import DownloadManager from '../components/community/DownloadManager';
import LocationTracker from '../components/community/LocationTracker';
import ErrorBoundary from '../components/common/ErrorBoundary';
import NetworkStatus from '../components/common/NetworkStatus';
import { useErrorHandler } from '../hooks/useErrorHandler';
import './CommunityDashboard.css';

export const CommunityDashboard: React.FC = () => {
  const { state, signOut, dispatch } = useApp();
  const { handleError } = useErrorHandler();
  const [activeTab, setActiveTab] = useState<'map' | 'download' | 'location'>('map');
  const [zones, setZones] = useState<ProtectedZone[]>([]);
  const [nearbyZones, setNearbyZones] = useState<ProtectedZone[]>([]);
  const [showGeofenceAlert, setShowGeofenceAlert] = useState(false);
  const [alertZone, setAlertZone] = useState<ProtectedZone | null>(null);

  useEffect(() => {
    loadCachedZones();
  }, []);

  useEffect(() => {
    if (state.currentLocation) {
      checkNearbyZones();
    }
  }, [state.currentLocation, zones]);

  const loadCachedZones = async () => {
    try {
      const cachedZones = await OfflineStorageService.getCachedZones();
      setZones(cachedZones);
    } catch (error) {
      console.error('Error loading cached zones:', error);
    }
  };

  const checkNearbyZones = async () => {
    if (!state.currentLocation) return;

    try {
      const violatedZones = await GeofencingService.checkLocationInZones(state.currentLocation);
      setNearbyZones(violatedZones);
      
      if (violatedZones.length > 0 && !showGeofenceAlert) {
        setAlertZone(violatedZones[0]);
        setShowGeofenceAlert(true);
      }
    } catch (error) {
      console.error('Error checking nearby zones:', error);
    }
  };

  const handleDismissAlert = () => {
    setShowGeofenceAlert(false);
    setAlertZone(null);
  };

  const toggleLocationTracking = async () => {
    if (state.isLocationTracking) {
      GeofencingService.stopLocationTracking();
      dispatch({ type: 'TOGGLE_LOCATION_TRACKING' });
    } else {
      try {
        const started = await GeofencingService.startLocationTracking(
          (coords) => {
            dispatch({ type: 'SET_LOCATION', payload: coords });
          },
          (zone: ProtectedZone, _coords: [number, number]) => {
            setAlertZone(zone);
            setShowGeofenceAlert(true);
          },
          true // Enable background tracking
        );
        
        if (started) {
          dispatch({ type: 'TOGGLE_LOCATION_TRACKING' });
        } else {
          alert('Location services are not available on this device');
        }
      } catch (error) {
        console.error('Failed to start location tracking:', error);
        alert('Failed to start location tracking. Please check your location permissions.');
      }
    }
  };

  return (
    <div className="community-dashboard">
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>🌊 Coastal Guard</h1>
            <span className="user-info">
              {state.user?.profile?.name || state.user?.email}
            </span>
          </div>
          <div className="header-right">
            <NetworkStatus variant="header" />
            <button 
              onClick={toggleLocationTracking}
              className={`location-btn ${state.isLocationTracking ? 'active' : ''}`}
            >
              {state.isLocationTracking ? '📍 Tracking' : '📍 Start Tracking'}
            </button>
            <button onClick={signOut} className="sign-out-btn">
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {showGeofenceAlert && alertZone && (
        <div className="geofence-alert">
          <div className="alert-content">
            <h3>⚠️ Protected Zone Alert</h3>
            <p>You have entered: <strong>{alertZone.properties?.name || alertZone.name}</strong></p>
            <p>{alertZone.properties?.description || alertZone.description}</p>
            <div className="alert-actions">
              <button onClick={handleDismissAlert} className="dismiss-btn">
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      <nav className="dashboard-nav">
        <button 
          className={activeTab === 'map' ? 'active' : ''}
          onClick={() => setActiveTab('map')}
        >
          🗺️ Map
        </button>
        <button 
          className={activeTab === 'download' ? 'active' : ''}
          onClick={() => setActiveTab('download')}
        >
          📥 Download Regions
        </button>
        <button 
          className={activeTab === 'location' ? 'active' : ''}
          onClick={() => setActiveTab('location')}
        >
          📍 Location Status
        </button>
      </nav>

      <main className="dashboard-content">
        {activeTab === 'map' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Map View')}
            fallback={<div className="error-fallback">Map failed to load. Please check your connection and try again.</div>}
          >
            <div className="map-tab">
              <MapboxMap
                zones={zones}
                userRole="community"
                currentLocation={state.currentLocation}
                isLocationTracking={state.isLocationTracking}
              />
              {nearbyZones.length > 0 && (
                <div className="nearby-zones-indicator">
                  <span>⚠️ {nearbyZones.length} protected zone(s) nearby</span>
                </div>
              )}
            </div>
          </ErrorBoundary>
        )}

        {activeTab === 'download' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Download Manager')}
            fallback={<div className="error-fallback">Download manager failed to load. Please try again.</div>}
          >
            <div className="download-tab">
              <DownloadManager />
            </div>
          </ErrorBoundary>
        )}

        {activeTab === 'location' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Location Tracker')}
            fallback={<div className="error-fallback">Location tracker failed to load. Please check location permissions.</div>}
          >
            <div className="location-tab">
              <LocationTracker />
            </div>
          </ErrorBoundary>
        )}
      </main>
    </div>
  );
};

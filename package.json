{"name": "coastal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mapbox/mapbox-gl-draw": "^1.5.0", "@supabase/supabase-js": "^2.49.8", "@turf/turf": "^7.2.0", "localforage": "^1.10.0", "mapbox-gl": "^3.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "workbox-background-sync": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/mapbox__mapbox-gl-draw": "^1.4.8", "@types/mapbox-gl": "^3.4.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "workbox-cli": "^7.3.0"}}
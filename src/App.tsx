import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AppProvider, useApp } from './store/AppContext';
import { AuthPage } from './pages/AuthPage';
import { AuthorityDashboard } from './pages/AuthorityDashboard';
import { CommunityDashboard } from './pages/CommunityDashboard';
import { LoadingSpinner } from './components/common/LoadingSpinner';
import ErrorBoundary from './components/common/ErrorBoundary';
import OfflineBanner from './components/common/OfflineBanner';
import { PushNotificationService } from './services/pushNotification';
import { useNetworkStatus } from './hooks/useNetworkStatus';
import { useErrorHandler } from './hooks/useErrorHandler';
import './App.css';

// Debug utility
if (process.env.NODE_ENV === 'development') {
  (window as any).debugAuth = () => {
    console.log('=== AUTH STATE DEBUG ===');
    console.log('Current URL:', window.location.href);
    console.log('Local Storage keys:', Object.keys(localStorage));
    
    const authKeys = Object.keys(localStorage).filter(key => 
      key.includes('supabase') || key.includes('auth')
    );
    
    authKeys.forEach(key => {
      console.log(`${key}:`, localStorage.getItem(key)?.substring(0, 100) + '...');
    });
    
    console.log('=== END AUTH DEBUG ===');
  };
}

const AppContent: React.FC = () => {
  const { state, dispatch } = useApp();
  const { isOnline } = useNetworkStatus();
  const { handleError } = useErrorHandler();

  useEffect(() => {
    // Update global online status
    dispatch({ type: 'SET_ONLINE_STATUS', payload: isOnline });
  }, [isOnline, dispatch]);

  useEffect(() => {
    // Clear errors when user state changes (successful auth)
    if (state.user) {
      dispatch({ type: 'SET_ERROR', payload: null });
    }
  }, [state.user, dispatch]);

  useEffect(() => {
    // Initialize push notification service
    const initializePushNotifications = async () => {
      try {
        const initialized = await PushNotificationService.initialize();
        if (initialized) {
          console.log('Push notification service initialized');
        }
      } catch (error) {
        console.warn('Failed to initialize push notifications:', error);
        // Continue without push notifications
      }
    };

    initializePushNotifications();
  }, []);

  // Global error handler for the app
  const handleGlobalError = (error: Error, errorInfo: React.ErrorInfo) => {
    handleError(error, 'Global App Error');
    console.error('Global error:', error, errorInfo);
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to Sentry, LogRocket, etc.
    }
  };

  if (state.isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <OfflineBanner isOnline={isOnline} />
      <Router>
        <div className="app">
          <Routes>
            <Route 
              path="/auth" 
              element={!state.user ? <AuthPage /> : <Navigate to="/" replace />} 
            />
            <Route 
              path="/" 
              element={
                state.user ? (
                  state.user.role === 'authority' ? (
                    <ErrorBoundary onError={handleGlobalError}>
                      <AuthorityDashboard />
                    </ErrorBoundary>
                  ) : (
                    <ErrorBoundary onError={handleGlobalError}>
                      <CommunityDashboard />
                    </ErrorBoundary>
                  )
                ) : (
                  <Navigate to="/auth" replace />
                )
              } 
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </>
  );
};

const App: React.FC = () => {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
};

export default App;

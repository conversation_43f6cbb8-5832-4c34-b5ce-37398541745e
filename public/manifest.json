{"name": "Coastal Marine Protection", "short_name": "CoastalGuard", "description": "Progressive Web App for coastal marine protection with offline geofencing capabilities", "start_url": "/", "display": "standalone", "theme_color": "#0066cc", "background_color": "#f8f9fa", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["navigation", "utilities", "government"], "screenshots": [{"src": "/screenshots/desktop-map.png", "type": "image/png", "sizes": "1280x720", "form_factor": "wide", "label": "Map view for authorities"}, {"src": "/screenshots/mobile-community.png", "type": "image/png", "sizes": "640x1136", "form_factor": "narrow", "label": "Community dashboard"}], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/badge-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "badge"}], "shortcuts": [{"name": "Map View", "short_name": "Map", "description": "View protected marine zones on map", "url": "/?tab=map", "icons": [{"src": "/icons/shortcut-map.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Download Regions", "short_name": "Download", "description": "Download map regions for offline use", "url": "/?tab=download", "icons": [{"src": "/icons/shortcut-download.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Location Status", "short_name": "Location", "description": "View current location and tracking status", "url": "/?tab=location", "icons": [{"src": "/icons/shortcut-location.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [{"platform": "web", "url": "https://your-app-domain.com/manifest.json"}], "prefer_related_applications": false, "protocol_handlers": [{"protocol": "geo", "url": "/?coordinates=%s"}], "handle_links": "preferred", "launch_handler": {"client_mode": "focus-existing"}, "edge_side_panel": {"preferred_width": 400}}
# Coastal Marine Protection PWA - Copilot Instructions

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
This is a Progressive Web App (PWA) for coastal marine protection, allowing authorities to manage protected marine zones and community users to receive offline geofencing alerts.

## Technology Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Mapping**: Mapbox GL JS + Mapbox GL Draw
- **Backend**: Supabase (Auth, PostgreSQL + PostGIS, Realtime)
- **Offline**: Workbox Service Worker + IndexedDB (localForage)
- **Geospatial**: Turf.js for point-in-polygon calculations
- **PWA**: Vite PWA plugin with Workbox

## Architecture Patterns
- **Component Structure**: Separate components for Authority and Community interfaces
- **State Management**: React Context for global state, local state for components
- **Offline-First**: Cache-first strategy for map tiles, background sync for data
- **Real-time**: Supabase Realtime channels for breach notifications

## Key Features to Implement
1. **Authority Interface**: Zone CRUD operations with map drawing tools
2. **Community Interface**: Offline navigation with geofence alerts
3. **Download System**: Pre-download map tiles and zone data for offline use
4. **Geofencing**: Real-time point-in-polygon detection using Turf.js
5. **Sync System**: Background sync for breach events when online
6. **PWA Features**: Service worker, web app manifest, installable

## Code Style Guidelines
- Use TypeScript strict mode
- Implement proper error handling for offline scenarios
- Follow React best practices (hooks, functional components)
- Use CSS modules or styled-components for styling
- Implement proper loading states and error boundaries
- Include comprehensive JSDoc comments for complex functions

## Performance Requirements
- Map render ≤100ms
- Geofence check ≤50ms
- Offline cache hit rate ≥98%
- Download region in ≤30s over 4G

## Security Considerations
- HTTPS only
- Supabase Row-Level Security for role-based access
- Sanitize all GeoJSON inputs
- Secure API key management

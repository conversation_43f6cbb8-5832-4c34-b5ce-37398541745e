/**
 * Coastal Marine Protection - Service Worker
 * Handles offline functionality, background sync, push notifications, and background location tracking
 */

/// <reference path="./types/sw.d.ts" />

import { precacheAndRoute, cleanupOutdatedCaches } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { CacheFirst, NetworkFirst, StaleWhileRevalidate } from 'workbox-strategies';
import { Queue } from 'workbox-background-sync';

// Declare service worker global scope
declare const self: ServiceWorkerGlobalScope & {
  __WB_MANIFEST: any;
  skipWaiting(): Promise<void>;
};

// Type definitions for location data
interface LocationPosition {
  coords: {
    latitude: number;
    longitude: number;
    accuracy: number;
    altitude?: number;
    altitudeAccuracy?: number;
    heading?: number;
    speed?: number;
  };
  timestamp: number;
}

// Precache static assets
precacheAndRoute(self.__WB_MANIFEST);
cleanupOutdatedCaches();

// Cache strategies for different types of content

// Cache map tiles with CacheFirst strategy (they rarely change)
registerRoute(
  ({ url }) => url.origin === 'https://api.mapbox.com' && url.pathname.includes('/tiles/'),
  new CacheFirst({
    cacheName: 'mapbox-tiles',
    plugins: [{
      cacheKeyWillBeUsed: async ({ request }) => {
        // Remove access token from cache key to avoid duplicates
        const url = new URL(request.url);
        url.searchParams.delete('access_token');
        return url.toString();
      }
    }]
  })
);

// Cache Mapbox style resources
registerRoute(
  ({ url }) => url.origin === 'https://api.mapbox.com' && (
    url.pathname.includes('/styles/') || 
    url.pathname.includes('/fonts/') ||
    url.pathname.includes('/sprites/')
  ),
  new CacheFirst({
    cacheName: 'mapbox-styles'
  })
);

// Cache API responses with NetworkFirst strategy
registerRoute(
  ({ url }) => url.origin === 'https://your-supabase-url.supabase.co',
  new NetworkFirst({
    cacheName: 'api-responses',
    networkTimeoutSeconds: 3
  })
);

// Cache app shell with StaleWhileRevalidate
registerRoute(
  ({ request }) => request.destination === 'document',
  new StaleWhileRevalidate({
    cacheName: 'app-shell'
  })
);

// Background sync for breach events
const breachEventQueue = new Queue('breach-events', {
  onSync: async ({ queue }) => {
    let entry;
    while ((entry = await queue.shiftRequest())) {
      try {
        await fetch(entry.request);
        console.log('Breach event synced successfully');
      } catch (error) {
        console.error('Failed to sync breach event:', error);
        // Re-add to queue for retry
        await queue.unshiftRequest(entry);
        throw error;
      }
    }
  }
});

// Listen for breach events to be queued
(self as any).addEventListener('message', (event: any) => {
  if (event.data && event.data.type === 'QUEUE_BREACH_EVENT') {
    const { url, method, body } = event.data.payload;
    
    const request = new Request(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': event.data.payload.authorization
      },
      body: JSON.stringify(body)
    });
    
    breachEventQueue.pushRequest({ request });
  }
  
  // Handle location updates
  if (event.data && event.data.type === 'LOCATION_UPDATE') {
    checkGeofencing(event.data.payload);
  }
});

// Location tracking queue for offline scenarios
const locationQueue = new Queue('location-updates', {
  onSync: async ({ queue }) => {
    let entry;
    while ((entry = await queue.shiftRequest())) {
      try {
        const response = await fetch(entry.request);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        console.log('Location update synced successfully');
      } catch (error) {
        console.error('Failed to sync location update:', error);
        // Re-add to queue for retry
        await queue.unshiftRequest(entry);
        throw error;
      }
    }
  }
});

// Push notification handling
(self as any).addEventListener('push', (event: any) => {
  const options: ExtendedNotificationOptions = {
    body: 'Marine protection zone breach detected',
    icon: '/pwa-192x192.png',
    badge: '/pwa-192x192.png',
    data: {
      url: '/',
      timestamp: Date.now()
    },
    actions: [
      {
        action: 'view',
        title: 'View Details'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ] as any,
    requireInteraction: true,
    silent: false
  };

  if (event.data) {
    try {
      const data = event.data.json();
      options.body = data.message || options.body;
      options.data = { ...options.data, ...data };
    } catch (e) {
      console.warn('Failed to parse push data:', e);
    }
  }

  event.waitUntil(
    (self as any).registration.showNotification('Coastal Marine Protection', options)
  );
});

// Handle notification clicks
(self as any).addEventListener('notificationclick', (event: any) => {
  event.notification.close();

  if (event.action === 'view') {
    // Open the app
    event.waitUntil(
      (self as any).clients.openWindow(event.notification.data.url || '/')
    );
  } else if (event.action === 'dismiss') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      (self as any).clients.openWindow('/')
    );
  }
});

// Geofencing check in service worker
const checkGeofencing = async (position: LocationPosition): Promise<void> => {
  try {
    // Get cached zones
    const cache = await caches.open('api-responses');
    const zonesResponse = await cache.match('/api/zones');
    
    if (!zonesResponse) {
      console.log('No cached zones available for geofencing check');
      return;
    }

    const zones = await zonesResponse.json();
    const { latitude, longitude } = position.coords;

    // Simple point-in-polygon check (basic implementation)
    for (const zone of zones) {
      if (isPointInPolygon([longitude, latitude], zone.geometry.coordinates[0])) {
        // Send breach notification
        await (self as any).registration.showNotification('Protected Zone Alert', {
          body: `You have entered: ${zone.properties.name}`,
          icon: '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          data: { zoneId: zone.id },
          requireInteraction: true
        });

        // Queue breach event for sync when online
        const breachData = {
          zone_id: zone.id,
          location: [longitude, latitude],
          timestamp: new Date().toISOString(),
          severity: zone.properties.severity || 'medium'
        };

        const request = new Request('/api/breach-events', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(breachData)
        });

        await breachEventQueue.pushRequest({ request });
        break;
      }
    }
  } catch (error) {
    console.error('Error in geofencing check:', error);
  }
};

// Simple point-in-polygon algorithm
const isPointInPolygon = (point: [number, number], polygon: number[][]): boolean => {
  const [x, y] = point;
  let inside = false;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];

    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }

  return inside;
};

// Periodic background sync for checking online status and syncing data
(self as any).addEventListener('sync', (event: any) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      Promise.all([
        breachEventQueue.replayRequests(),
        locationQueue.replayRequests()
      ])
    );
  }
});

// Installation and activation
(self as any).addEventListener('install', (_event: any) => {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

(self as any).addEventListener('activate', (event: any) => {
  console.log('Service Worker activating...');
  event.waitUntil((self as any).clients.claim());
});

// Handle failed requests and add to queue for retry
(self as any).addEventListener('fetch', (event: any) => {
  // Only handle API requests for background sync
  if (event.request.url.includes('/api/') && 
      event.request.method === 'POST' && 
      !navigator.onLine) {
    
    event.respondWith(
      Promise.resolve(new Response(
        JSON.stringify({ queued: true, message: 'Request queued for sync when online' }),
        { 
          status: 202,
          headers: { 'Content-Type': 'application/json' }
        }
      ))
    );

    // Add to appropriate queue
    if (event.request.url.includes('/breach-events')) {
      breachEventQueue.pushRequest({ request: event.request.clone() });
    } else if (event.request.url.includes('/location-updates')) {
      locationQueue.pushRequest({ request: event.request.clone() });
    }
  }
});

console.log('Coastal Marine Protection Service Worker loaded');

-- Coastal Marine Protection Database Schema - Final Clean Version
-- Run this after resetting your Supabase database

-- Enable PostGIS extension for geospatial data
CREATE EXTENSION IF NOT EXISTS postgis;

-- Disable RLS for development (remove for production)
ALTER DATABASE postgres SET row_security = off;

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('authority', 'community');
CREATE TYPE zone_status AS ENUM ('active', 'inactive', 'draft');
CREATE TYPE breach_severity AS ENUM ('low', 'medium', 'high', 'critical');

-- User profiles table (extends Supabase auth.users)
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  name TEXT NOT NULL,
  role user_role NOT NULL DEFAULT 'community',
  organization TEXT,
  phone TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Protected zones table
CREATE TABLE protected_zones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  geometry GEOMETRY(POLYGON, 4326) NOT NULL,
  properties JSONB DEFAULT '{}',
  status zone_status DEFAULT 'active',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Breach events table
CREATE TABLE breach_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  zone_id UUID REFERENCES protected_zones(id),
  user_id UUID REFERENCES auth.users(id),
  location GEOMETRY(POINT, 4326) NOT NULL,
  severity breach_severity DEFAULT 'medium',
  acknowledged BOOLEAN DEFAULT FALSE,
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Download regions table (for offline map data)
CREATE TABLE download_regions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  bounds GEOMETRY(POLYGON, 4326) NOT NULL,
  zoom_levels INTEGER[] DEFAULT ARRAY[10,11,12,13,14,15],
  size_mb DECIMAL(10,2),
  downloaded_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_protected_zones_geometry ON protected_zones USING GIST (geometry);
CREATE INDEX idx_breach_events_location ON breach_events USING GIST (location);
CREATE INDEX idx_breach_events_zone_id ON breach_events (zone_id);
CREATE INDEX idx_breach_events_created_at ON breach_events (created_at);
CREATE INDEX idx_download_regions_bounds ON download_regions USING GIST (bounds);
CREATE INDEX idx_download_regions_user_id ON download_regions (user_id);

-- Functions for common operations

-- Function to check if a point is within any protected zone
CREATE OR REPLACE FUNCTION check_point_in_zones(lat DECIMAL, lng DECIMAL)
RETURNS TABLE(zone_id UUID, zone_name TEXT, severity breach_severity) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pz.id,
    pz.name,
    CASE 
      WHEN pz.properties->>'severity' = 'critical' THEN 'critical'::breach_severity
      WHEN pz.properties->>'severity' = 'high' THEN 'high'::breach_severity
      WHEN pz.properties->>'severity' = 'medium' THEN 'medium'::breach_severity
      ELSE 'low'::breach_severity
    END
  FROM protected_zones pz
  WHERE ST_Contains(pz.geometry, ST_SetSRID(ST_MakePoint(lng, lat), 4326))
    AND pz.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a breach event
CREATE OR REPLACE FUNCTION create_breach_event(
  p_zone_id UUID,
  p_user_id UUID,
  p_lat DECIMAL,
  p_lng DECIMAL,
  p_severity breach_severity DEFAULT 'medium',
  p_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
  breach_id UUID;
BEGIN
  INSERT INTO breach_events (zone_id, user_id, location, severity, metadata)
  VALUES (
    p_zone_id,
    p_user_id,
    ST_SetSRID(ST_MakePoint(p_lng, p_lat), 4326),
    p_severity,
    p_metadata
  )
  RETURNING id INTO breach_id;
  
  RETURN breach_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get zones within a bounding box (for map queries)
CREATE OR REPLACE FUNCTION get_zones_in_bounds(
  min_lng DECIMAL,
  min_lat DECIMAL,
  max_lng DECIMAL,
  max_lat DECIMAL
) RETURNS SETOF protected_zones AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM protected_zones pz
  WHERE ST_Intersects(
    pz.geometry,
    ST_MakeEnvelope(min_lng, min_lat, max_lng, max_lat, 4326)
  )
  AND pz.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_protected_zones_updated_at
  BEFORE UPDATE ON protected_zones
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Disable RLS on all tables for development
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE protected_zones DISABLE ROW LEVEL SECURITY;
ALTER TABLE breach_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE download_regions DISABLE ROW LEVEL SECURITY;

-- Insert sample data for testing - San Vicente, Palawan, Philippines
INSERT INTO protected_zones (name, description, geometry, properties) VALUES 
(
  'Long Beach Marine Reserve',
  'Protected marine area around Long Beach coastline',
  ST_GeomFromText('POLYGON((119.130 18.170, 119.140 18.170, 119.140 18.160, 119.130 18.160, 119.130 18.170))', 4326),
  '{"severity": "high", "restrictions": ["fishing", "swimming", "boating"]}'
),
(
  'Port Barton Marine Sanctuary',
  'Critical breeding area for marine life and coral conservation',
  ST_GeomFromText('POLYGON((119.510 10.470, 119.530 10.470, 119.530 10.450, 119.510 10.450, 119.510 10.470))', 4326),
  '{"severity": "critical", "restrictions": ["all_activities"]}'
),
(
  'El Nido Bacuit Bay Protected Area',
  'Marine protected area for coral reef ecosystem',
  ST_GeomFromText('POLYGON((119.380 11.200, 119.420 11.200, 119.420 11.160, 119.380 11.160, 119.380 11.200))', 4326),
  '{"severity": "medium", "restrictions": ["commercial_fishing", "motorized_boats"]}'
),
(
  'San Vicente Bay Conservation Zone',
  'Tourist and fishing regulation area',
  ST_GeomFromText('POLYGON((119.120 18.180, 119.150 18.180, 119.150 18.150, 119.120 18.150, 119.120 18.180))', 4326),
  '{"severity": "low", "restrictions": ["motorized_boats"]}'
),
(
  'Taytay Marine Protected Area',
  'Multi-use conservation zone with fishing restrictions',
  ST_GeomFromText('POLYGON((119.500 10.820, 119.520 10.820, 119.520 10.800, 119.500 10.800, 119.500 10.820))', 4326),
  '{"severity": "medium", "restrictions": ["commercial_fishing", "night_fishing"]}'
),
(
  'Coron Island Marine Sanctuary',
  'Critical coral reef and fish spawning area',
  ST_GeomFromText('POLYGON((120.200 12.000, 120.230 12.000, 119.230 11.970, 120.200 11.970, 120.200 12.000))', 4326),
  '{"severity": "critical", "restrictions": ["all_activities", "research_permit_required"]}'
),
(
  'Linapacan Strait Conservation Area',
  'Deep water marine corridor protection',
  ST_GeomFromText('POLYGON((119.800 11.400, 119.850 11.400, 119.850 11.350, 119.800 11.350, 119.800 11.400))', 4326),
  '{"severity": "high", "restrictions": ["commercial_fishing", "cargo_ships", "oil_drilling"]}'
),
(
  'Balabac Island Marine Reserve',
  'Endemic species protection zone',
  ST_GeomFromText('POLYGON((117.050 7.980, 117.080 7.980, 117.080 7.950, 117.050 7.950, 117.050 7.980))', 4326),
  '{"severity": "critical", "restrictions": ["all_activities", "scientific_research_only"]}'
),
(
  'Dumaran Island Coastal Zone',
  'Mangrove and seagrass conservation area',
  ST_GeomFromText('POLYGON((119.780 10.550, 119.820 10.550, 119.820 10.510, 119.780 10.510, 119.780 10.550))', 4326),
  '{"severity": "medium", "restrictions": ["mangrove_cutting", "dynamite_fishing", "fine_mesh_nets"]}'
),
(
  'Roxas Marine Sanctuary',
  'Community-managed fisheries area',
  ST_GeomFromText('POLYGON((119.350 10.320, 119.380 10.320, 119.380 10.290, 119.350 10.290, 119.350 10.320))', 4326),
  '{"severity": "low", "restrictions": ["trawling", "compressor_fishing"]}'
),
(
  'Brookes Point Turtle Nesting Beach',
  'Sea turtle conservation area',
  ST_GeomFromText('POLYGON((117.800 8.780, 117.830 8.780, 117.830 8.750, 117.800 8.750, 117.800 8.780))', 4326),
  '{"severity": "high", "restrictions": ["beach_access_restricted", "no_lights", "no_vehicles"]}'
),
(
  'Bataraza Seagrass Meadows',
  'Dugong feeding area protection',
  ST_GeomFromText('POLYGON((117.600 8.650, 117.650 8.650, 117.650 8.600, 117.600 8.600, 117.600 8.650))', 4326),
  '{"severity": "high", "restrictions": ["boat_anchoring", "seagrass_harvesting", "motorboat_access"]}'
);

-- Verification queries
SELECT 'Database setup completed successfully' as status;
SELECT count(*) as total_zones FROM protected_zones;
SELECT count(*) as total_profiles FROM user_profiles;

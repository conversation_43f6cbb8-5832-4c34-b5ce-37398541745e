.community-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--light-gray);
}

.community-header {
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
}

.community-header h1 {
  margin: 0;
  color: var(--primary-blue);
  font-size: 1.5rem;
  font-weight: 600;
}

.community-nav {
  background-color: var(--white);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  overflow-x: auto;
}

.community-nav-button {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--medium-gray);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.community-nav-button:hover {
  color: var(--primary-blue);
  background-color: var(--light-gray);
}

.community-nav-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

.community-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.status-bar {
  background-color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.location-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.location-status.active {
  color: var(--success-green);
}

.location-status.inactive {
  color: var(--warning-orange);
}

.location-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

.offline-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--medium-gray);
}

.offline-indicator.offline {
  color: var(--warning-orange);
}

.offline-indicator.online {
  color: var(--success-green);
}

/* Map container specific to community dashboard */
.community-map-container {
  flex: 1;
  position: relative;
}

.community-map-container .mapboxgl-map {
  width: 100%;
  height: 100%;
}

/* Zone alert overlay */
.zone-alert {
  position: absolute;
  top: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--danger-red);
  color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  animation: alertPulse 1s infinite;
  max-width: 300px;
  text-align: center;
}

.zone-alert h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 16px;
}

.zone-alert p {
  margin: 0;
  font-size: 14px;
}

@keyframes alertPulse {
  0%, 100% {
    box-shadow: var(--shadow-lg);
  }
  50% {
    box-shadow: var(--shadow-lg), 0 0 20px rgba(231, 76, 60, 0.5);
  }
}

/* Location controls */
.location-controls {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.location-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background-color: var(--white);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
  font-size: 18px;
  transition: all 0.2s ease;
}

.location-button:hover {
  background-color: var(--primary-blue);
  color: var(--white);
}

.location-button:active {
  transform: scale(0.95);
}

.location-button.active {
  background-color: var(--success-green);
  color: var(--white);
}

/* Responsive design */
@media (max-width: 768px) {
  .community-header h1 {
    font-size: 1.25rem;
  }
  
  .community-nav-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
  }
  
  .zone-alert {
    max-width: calc(100% - 2rem);
    left: var(--spacing-md);
    right: var(--spacing-md);
    transform: none;
  }
  
  .location-controls {
    bottom: var(--spacing-sm);
    right: var(--spacing-sm);
  }
}

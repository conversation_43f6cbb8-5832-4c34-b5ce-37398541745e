import * as turf from '@turf/turf';
import type { ProtectedZone, BreachEvent } from '../types';
import { OfflineStorageService } from './offlineStorage';
import { BackgroundLocationService } from './backgroundLocation';

export class GeofencingService {
  private static locationWatchId: number | null = null;
  private static onLocationUpdate?: (coords: [number, number]) => void;
  private static onGeofenceAlert?: (zone: ProtectedZone, coords: [number, number]) => void;
  private static isBackgroundModeEnabled = false;

  /**
   * Start enhanced location tracking with background service integration
   */
  static async startLocationTracking(
    onLocationUpdate: (coords: [number, number]) => void,
    onGeofenceAlert: (zone: ProtectedZone, coords: [number, number]) => void,
    enableBackground = true
  ): Promise<boolean> {
    if (!navigator.geolocation) {
      console.error('Geolocation not supported');
      return false;
    }

    this.onLocationUpdate = onLocationUpdate;
    this.onGeofenceAlert = onGeofenceAlert;

    // Start foreground tracking
    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 5000
    };

    this.locationWatchId = navigator.geolocation.watchPosition(
      this.handleLocationUpdate.bind(this),
      this.handleLocationError.bind(this),
      options
    );

    // Start background tracking if enabled
    if (enableBackground) {
      try {
        const backgroundStarted = await BackgroundLocationService.startBackgroundTracking({
          backgroundMode: true,
          distanceFilter: 10, // 10 meters minimum movement
          interval: 15000 // 15 seconds
        });
        
        if (backgroundStarted) {
          this.isBackgroundModeEnabled = true;
          console.log('Background location tracking started');
        }
      } catch (error) {
        console.warn('Background location tracking failed to start:', error);
        // Continue with foreground tracking only
      }
    }

    return true;
  }

  /**
   * Stop location tracking (both foreground and background)
   */
  static stopLocationTracking(): void {
    // Stop foreground tracking
    if (this.locationWatchId !== null) {
      navigator.geolocation.clearWatch(this.locationWatchId);
      this.locationWatchId = null;
    }

    // Stop background tracking
    if (this.isBackgroundModeEnabled) {
      BackgroundLocationService.stopBackgroundTracking();
      this.isBackgroundModeEnabled = false;
    }
  }

  /**
   * Handle location updates and check for geofence violations
   */
  private static async handleLocationUpdate(position: GeolocationPosition): Promise<void> {
    const coords: [number, number] = [position.coords.longitude, position.coords.latitude];
    
    // Update location callback
    if (this.onLocationUpdate) {
      this.onLocationUpdate(coords);
    }

    // Check for geofence violations
    await this.checkGeofenceViolation(coords);
  }

  /**
   * Handle location errors
   */
  private static handleLocationError(error: GeolocationPositionError): void {
    console.error('Location error:', error.message);
  }

  /**
   * Check if current location violates any protected zones
   */
  static async checkGeofenceViolation(coords: [number, number]): Promise<void> {
    try {
      const zones = await OfflineStorageService.getCachedZones();
      const userPoint = turf.point(coords);

      for (const zone of zones) {
        if (this.isPointInZone(userPoint, zone)) {
          // Breach detected!
          await this.handleBreachEvent(zone, coords);
          
          if (this.onGeofenceAlert) {
            this.onGeofenceAlert(zone, coords);
          }
        }
      }
    } catch (error) {
      console.error('Error checking geofence:', error);
    }
  }

  /**
   * Check if a point is inside a protected zone using Turf.js
   */
  private static isPointInZone(point: any, zone: ProtectedZone): boolean {
    try {
      // Create a proper GeoJSON feature for turf.js
      const geoJsonZone = {
        type: 'Feature' as const,
        geometry: zone.geometry,
        properties: zone.properties
      };
      return turf.booleanPointInPolygon(point, geoJsonZone);
    } catch (error) {
      console.error('Error in point-in-polygon calculation:', error);
      return false;
    }
  }

  /**
   * Handle breach event - store locally and queue for sync
   */
  private static async handleBreachEvent(zone: ProtectedZone, coords: [number, number]): Promise<void> {
    const breachEvent: BreachEvent = {
      id: `breach_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      zone_id: zone.id,
      user_id: 'current_user_id', // This should come from auth context
      location: {
        coordinates: coords
      },
      created_at: new Date().toISOString(),
      synced: false,
      // Legacy support
      userId: 'current_user_id',
      zoneId: zone.id,
      coordinates: coords,
      timestamp: new Date().toISOString(),
      deviceId: this.getDeviceId()
    };

    // Store in offline queue
    await OfflineStorageService.queueBreachEvent(breachEvent);
    
    console.log('Breach event recorded:', breachEvent);
  }

  /**
   * Get or generate device ID for breach tracking
   */
  private static getDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }

  /**
   * Manual geofence check for a specific location
   */
  static async checkLocationInZones(coords: [number, number]): Promise<ProtectedZone[]> {
    const zones = await OfflineStorageService.getCachedZones();
    const userPoint = turf.point(coords);
    const violatedZones: ProtectedZone[] = [];

    for (const zone of zones) {
      if (this.isPointInZone(userPoint, zone)) {
        violatedZones.push(zone);
      }
    }

    return violatedZones;
  }

  /**
   * Get location tracking status
   */
  static getTrackingStatus(): {
    isTracking: boolean;
    isBackgroundEnabled: boolean;
    lastUpdate?: string;
  } {
    return {
      isTracking: this.locationWatchId !== null,
      isBackgroundEnabled: this.isBackgroundModeEnabled,
      lastUpdate: BackgroundLocationService.getLastUpdateTime()
    };
  }

  /**
   * Get location history from background service
   */
  static async getLocationHistory(startTime: string, endTime: string) {
    return BackgroundLocationService.getLocationHistory(startTime, endTime);
  }

  /**
   * Export location data
   */
  static async exportLocationData(): Promise<string> {
    return BackgroundLocationService.exportLocationData();
  }

  /**
   * Calculate distance to nearest zone boundary
   */
  static async getDistanceToNearestZone(coords: [number, number]): Promise<{
    zone: ProtectedZone;
    distance: number;
  } | null> {
    const zones = await OfflineStorageService.getCachedZones();
    const userPoint = turf.point(coords);
    let nearest: { zone: ProtectedZone; distance: number } | null = null;

    for (const zone of zones) {
      try {
        const distance = turf.pointToPolygonDistance(userPoint, zone, { units: 'meters' });
        
        if (!nearest || distance < nearest.distance) {
          nearest = { zone, distance };
        }
      } catch (error) {
        console.error('Error calculating distance to zone:', error);
      }
    }

    return nearest;
  }
}

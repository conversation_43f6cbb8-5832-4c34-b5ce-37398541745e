// Database types for Supabase
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          name: string;
          role: 'authority' | 'community';
          organization?: string;
          phone?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          name: string;
          role: 'authority' | 'community';
          organization?: string;
          phone?: string;
        };
        Update: {
          name?: string;
          role?: 'authority' | 'community';
          organization?: string;
          phone?: string;
        };
      };
      protected_zones: {
        Row: {
          id: string;
          name: string;
          description?: string;
          geometry: any; // PostGIS geometry
          properties: Record<string, any>;
          status: 'active' | 'inactive' | 'draft';
          created_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          name: string;
          description?: string;
          geometry: any;
          properties?: Record<string, any>;
          status?: 'active' | 'inactive' | 'draft';
          created_by?: string;
        };
        Update: {
          name?: string;
          description?: string;
          geometry?: any;
          properties?: Record<string, any>;
          status?: 'active' | 'inactive' | 'draft';
        };
      };
      breach_events: {
        Row: {
          id: string;
          zone_id?: string;
          user_id?: string;
          location: any; // PostGIS point
          severity: 'low' | 'medium' | 'high' | 'critical';
          acknowledged: boolean;
          acknowledged_by?: string;
          acknowledged_at?: string;
          metadata: Record<string, any>;
          created_at: string;
        };
        Insert: {
          zone_id?: string;
          user_id?: string;
          location: any;
          severity?: 'low' | 'medium' | 'high' | 'critical';
          metadata?: Record<string, any>;
        };
        Update: {
          acknowledged?: boolean;
          acknowledged_by?: string;
          acknowledged_at?: string;
          metadata?: Record<string, any>;
        };
      };
      download_regions: {
        Row: {
          id: string;
          user_id?: string;
          name: string;
          bounds: any; // PostGIS polygon
          zoom_levels: number[];
          size_mb?: number;
          downloaded_at?: string;
          expires_at?: string;
          created_at: string;
        };
        Insert: {
          user_id?: string;
          name: string;
          bounds: any;
          zoom_levels?: number[];
          size_mb?: number;
          downloaded_at?: string;
          expires_at?: string;
        };
        Update: {
          name?: string;
          bounds?: any;
          zoom_levels?: number[];
          size_mb?: number;
          downloaded_at?: string;
          expires_at?: string;
        };
      };
    };
  };
}

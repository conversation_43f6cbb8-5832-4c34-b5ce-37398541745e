import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { AppState, User } from '../types';
import { supabase } from '../services/supabase';
import { OfflineStorageService } from '../services/offlineStorage';

interface AppAction {
  type: 'SET_USER' | 'SET_ONLINE_STATUS' | 'SET_LOCATION' | 'TOGGLE_LOCATION_TRACKING' | 'SET_DOWNLOADED_REGIONS' | 'SET_ERROR' | 'SET_LOADING';
  payload?: any;
}

const initialState: AppState = {
  user: null,
  isOnline: navigator.onLine,
  downloadedRegions: [],
  currentLocation: null,
  isLocationTracking: false,
  error: null,
  isLoading: false
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_ONLINE_STATUS':
      return { ...state, isOnline: action.payload };
    case 'SET_LOCATION':
      return { ...state, currentLocation: action.payload };
    case 'TOGGLE_LOCATION_TRACKING':
      return { ...state, isLocationTracking: !state.isLocationTracking };
    case 'SET_DOWNLOADED_REGIONS':
      return { ...state, downloadedRegions: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  user: User | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, role: 'authority' | 'community') => Promise<void>;
  signOut: () => Promise<void>;
} | null>(null);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    // Set initial loading state
    dispatch({ type: 'SET_LOADING', payload: true });
    
    // Initialize authentication - prioritize session check over connection check
    const initializeAuth = async () => {
      try {
        console.log('AppContext: Initializing authentication...');
        
        // Always try to check for existing session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('AppContext: Session check result:', { hasSession: !!session?.user, error: sessionError });
        
        // If we have a valid session, proceed with it regardless of other issues
        if (session?.user && !sessionError) {
          console.log('AppContext: Found existing session, fetching profile...');
          await fetchUserProfile(session.user.id);
          return;
        }

        // If we have a session error, log it but don't necessarily fail
        if (sessionError) {
          console.warn('AppContext: Session check error (but continuing):', sessionError);
        }

        // No valid session found - user needs to authenticate
        console.log('AppContext: No valid session found, user needs to authenticate');
        dispatch({ type: 'SET_LOADING', payload: false });
        
      } catch (error) {
        console.error('AppContext: Auth initialization error:', error);
        // Always ensure loading is stopped, even on error
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('AppContext: Auth state change:', event, session?.user?.id);
      
      // Clear any previous errors on auth state change
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        dispatch({ type: 'SET_ERROR', payload: null });
      }
      
      // Set loading true for any auth event that requires processing
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        dispatch({ type: 'SET_LOADING', payload: true });
      }
      
      if (session?.user) {
        // Ensure loading is set for profile fetch
        dispatch({ type: 'SET_LOADING', payload: true });
        await fetchUserProfile(session.user.id);
      } else {
        console.log('AppContext: No session, clearing user state');
        dispatch({ type: 'SET_USER', payload: null });
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    });

    // Load downloaded regions from storage
    loadDownloadedRegions();

    // Listen for online/offline status
    const handleOnline = () => dispatch({ type: 'SET_ONLINE_STATUS', payload: true });
    const handleOffline = () => dispatch({ type: 'SET_ONLINE_STATUS', payload: false });

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      subscription.unsubscribe();
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const fetchUserProfile = async (userId: string) => {
    console.log('AppContext: Fetching user profile for:', userId);
    try {
      // Get user data from auth.users and user_profiles
      const { data: authData, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        console.error('AppContext: Auth user fetch error:', authError);
        // If we can't get the auth user, still try to create a basic user
        // This ensures we don't get stuck in a loading loop
        const basicUser: User = {
          id: userId,
          email: '<EMAIL>',
          role: 'community',
          profile: {
            name: 'User',
            organization: undefined
          }
        };
        dispatch({ type: 'SET_USER', payload: basicUser });
        dispatch({ type: 'SET_LOADING', payload: false });
        return;
      }
      
      // Try to get existing profile
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.log('AppContext: Profile fetch error:', error);
        // Create a user object with auth data, regardless of profile creation success
        const user: User = {
          id: userId,
          email: authData.user?.email || '',
          role: 'community', // Default role
          profile: {
            name: authData.user?.user_metadata?.name || authData.user?.email?.split('@')[0] || 'User',
            organization: undefined
          }
        };

        dispatch({ type: 'SET_USER', payload: user });
        dispatch({ type: 'SET_LOADING', payload: false });
        console.log('AppContext: User state updated with fallback profile');
        return;
      }

      console.log('AppContext: Existing profile found:', data);
      const user: User = {
        id: data.id,
        email: authData.user?.email || '',
        role: data.role,
        profile: {
          name: data.name,
          organization: data.organization
        }
      };

      dispatch({ type: 'SET_USER', payload: user });
      dispatch({ type: 'SET_LOADING', payload: false });
      console.log('AppContext: User state updated with existing profile');
    } catch (error) {
      console.error('AppContext: Error in fetchUserProfile:', error);
      // Create a minimal user to prevent auth loop - this ensures the app works
      const emergencyUser: User = {
        id: userId,
        email: '<EMAIL>',
        role: 'community',
        profile: {
          name: 'User',
          organization: undefined
        }
      };
      dispatch({ type: 'SET_USER', payload: emergencyUser });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const loadDownloadedRegions = async () => {
    try {
      const regions = await OfflineStorageService.getDownloadedRegions();
      dispatch({ type: 'SET_DOWNLOADED_REGIONS', payload: regions });
    } catch (error) {
      console.error('Error loading downloaded regions:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('AppContext: Starting sign in process...');
    dispatch({ type: 'SET_ERROR', payload: null }); // Clear any previous errors
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('AppContext: Sign in error:', error);
        // Provide user-friendly error messages
        let userMessage = error.message;
        if (error.message.includes('Invalid login credentials')) {
          userMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.message.includes('Network')) {
          userMessage = 'Network error. Please check your internet connection and try again.';
        }
        throw new Error(userMessage);
      }
      console.log('AppContext: Sign in successful, auth state change will trigger profile fetch');
      // Note: User state will be updated via the auth state change listener
    } catch (error) {
      console.error('AppContext: Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, role: 'authority' | 'community') => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password
    });

    if (error) throw error;

    if (data.user) {
      // Create profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: data.user.id,
          name: data.user.user_metadata?.name || email.split('@')[0],
          role
        });

      if (profileError) throw profileError;
    }
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  return (
    <AppContext.Provider
      value={{
        state,
        dispatch,
        user: state.user,
        signIn,
        signUp,
        signOut
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};

export const useAppContext = useApp;

.download-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.download-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.download-manager-header h2 {
  margin: 0;
  color: var(--dark-gray);
  font-size: 1.25rem;
}

.storage-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--medium-gray);
}

.storage-usage {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.storage-bar {
  width: 100px;
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.storage-fill {
  height: 100%;
  background-color: var(--primary-blue);
  transition: width 0.3s ease;
}

.storage-fill.warning {
  background-color: var(--warning-orange);
}

.storage-fill.danger {
  background-color: var(--danger-red);
}

.download-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.download-stat-card {
  background-color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.download-stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin: 0;
}

.download-stat-label {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: var(--spacing-xs) 0 0 0;
}

.map-regions {
  flex: 1;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.map-regions-header {
  background-color: var(--light-gray);
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-regions-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-gray);
}

.region-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.region-selector {
  position: relative;
}

.select-region-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-blue);
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.select-region-btn:hover {
  background-color: var(--secondary-blue);
}

.regions-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.region-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.region-item:hover {
  background-color: var(--light-gray);
}

.region-item:last-child {
  border-bottom: none;
}

.region-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.region-name {
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 14px;
}

.region-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.region-status.downloaded {
  background-color: #d4edda;
  color: #155724;
}

.region-status.downloading {
  background-color: #fff3cd;
  color: #856404;
}

.region-status.pending {
  background-color: #d1ecf1;
  color: #0c5460;
}

.region-status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.region-details {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.85rem;
  color: var(--medium-gray);
  margin-top: var(--spacing-xs);
}

.region-detail {
  font-size: 12px;
  color: var(--medium-gray);
}

.region-detail-label {
  font-weight: 500;
  color: var(--dark-gray);
}

.download-progress {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: var(--spacing-lg);
  margin: var(--spacing-md) 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.download-progress h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--dark-gray);
  font-size: 1.1rem;
  font-weight: 600;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.progress-stats span {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-blue) 0%, #4dabf7 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-percentage {
  text-align: center;
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 1rem;
}

.current-tile {
  font-size: 0.8rem;
  color: var(--medium-gray);
  text-align: center;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xs);
  border-radius: 4px;
}

.download-stats {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.download-stats h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--dark-gray);
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--medium-gray);
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  color: var(--dark-gray);
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .download-manager {
    padding: var(--spacing-sm);
  }
  
  .download-manager-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .storage-info {
    justify-content: center;
  }
  
  .download-stats {
    grid-template-columns: 1fr;
  }
  
  .map-regions-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
  
  .region-controls {
    justify-content: center;
  }
  
  .region-details {
    grid-template-columns: 1fr;
  }
  
  .preset-regions {
    grid-template-columns: 1fr;
  }
}

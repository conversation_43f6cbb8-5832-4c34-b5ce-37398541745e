import React, { useState, useEffect } from 'react';
import { useApp } from '../../store/AppContext';
import { GeofencingService } from '../../services/geofencing';
import { PushNotificationService } from '../../services/pushNotification';
import './LocationTracker.css';

export const LocationTracker: React.FC = () => {
  const { state } = useApp();
  const [nearestZone, setNearestZone] = useState<any>(null);
  const [locationAccuracy] = useState<number | null>(null);
  const [trackingStatus, setTrackingStatus] = useState<any>(null);
  const [locationHistory, setLocationHistory] = useState<any[]>([]);
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState(false);

  useEffect(() => {
    if (state.currentLocation) {
      checkNearestZone();
    }
    updateTrackingStatus();
  }, [state.currentLocation]);

  useEffect(() => {
    // Check initial notification permissions
    checkNotificationStatus();
    
    // Update tracking status periodically
    const statusInterval = setInterval(updateTrackingStatus, 5000);
    
    return () => clearInterval(statusInterval);
  }, []);

  const updateTrackingStatus = () => {
    const status = GeofencingService.getTrackingStatus();
    setTrackingStatus(status);
  };

  const checkNotificationStatus = () => {
    const isEnabled = PushNotificationService.isNotificationEnabled();
    setIsNotificationsEnabled(isEnabled);
  };

  const requestNotificationPermission = async () => {
    try {
      const subscription = await PushNotificationService.requestPermissionAndSubscribe();
      if (subscription) {
        setIsNotificationsEnabled(true);
        // Show test notification
        await PushNotificationService.showLocalNotification({
          title: 'Notifications enabled',
          body: 'You will now receive alerts when entering protected zones',
          icon: '/pwa-192x192.png'
        });
      }
    } catch (error) {
      console.error('Failed to enable notifications:', error);
      alert('Failed to enable notifications. Please check your browser settings.');
    }
  };

  const loadLocationHistory = async () => {
    try {
      const endTime = new Date().toISOString();
      const startTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // Last 24 hours
      const history = await GeofencingService.getLocationHistory(startTime, endTime);
      setLocationHistory(history.slice(-10)); // Show last 10 updates
    } catch (error) {
      console.error('Failed to load location history:', error);
    }
  };

  const exportData = async () => {
    try {
      const data = await GeofencingService.exportLocationData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `location-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  };

  const checkNearestZone = async () => {
    if (!state.currentLocation) return;

    try {
      const nearest = await GeofencingService.getDistanceToNearestZone(state.currentLocation);
      setNearestZone(nearest);
    } catch (error) {
      console.error('Error checking nearest zone:', error);
    }
  };

  return (
    <div className="location-tracker">
      <div className="tracker-header">
        <h2>📍 Location Tracker</h2>
        <p>Monitor your position relative to protected zones</p>
      </div>

      <div className="location-status">
        <div className="status-card">
          <h3>Tracking Status</h3>
          <div className={`status-indicator ${state.isLocationTracking ? 'active' : 'inactive'}`}>
            {state.isLocationTracking ? '🟢 Active' : '🔴 Inactive'}
          </div>
          {trackingStatus && (
            <div className="tracking-details">
              <p>Background: {trackingStatus.isBackgroundEnabled ? '✅ Enabled' : '❌ Disabled'}</p>
              {trackingStatus.lastUpdate && (
                <p>Last update: {new Date(trackingStatus.lastUpdate).toLocaleTimeString()}</p>
              )}
            </div>
          )}
          {state.isLocationTracking && (
            <p>Location updates every 10 seconds</p>
          )}
        </div>

        <div className="status-card">
          <h3>Current Position</h3>
          {state.currentLocation ? (
            <div>
              <p>Lat: {state.currentLocation[1].toFixed(6)}</p>
              <p>Lng: {state.currentLocation[0].toFixed(6)}</p>
              {locationAccuracy && (
                <p>Accuracy: ±{locationAccuracy}m</p>
              )}
            </div>
          ) : (
            <p>No location data</p>
          )}
        </div>

        <div className="status-card">
          <h3>Notifications</h3>
          <div className={`status-indicator ${isNotificationsEnabled ? 'active' : 'inactive'}`}>
            {isNotificationsEnabled ? '🔔 Enabled' : '🔕 Disabled'}
          </div>
          {!isNotificationsEnabled && (
            <button 
              onClick={requestNotificationPermission}
              className="enable-notifications-btn"
            >
              Enable Notifications
            </button>
          )}
        </div>
      </div>

      {nearestZone && (
        <div className="nearest-zone">
          <h3>Nearest Protected Zone</h3>
          <div className="zone-card">
            <h4>{nearestZone.zone.properties.name}</h4>
            <p>Distance: {Math.round(nearestZone.distance)}m</p>
            <p>{nearestZone.zone.properties.description}</p>
          </div>
        </div>
      )}

      <div className="settings-section">
        <h3>Data Management</h3>
        
        <div className="data-actions">
          <button 
            onClick={loadLocationHistory}
            className="action-btn"
          >
            📊 Load Recent History
          </button>
          
          <button 
            onClick={exportData}
            className="action-btn"
          >
            📤 Export Location Data
          </button>
        </div>

        {locationHistory.length > 0 && (
          <div className="location-history">
            <h4>Recent Locations ({locationHistory.length})</h4>
            <div className="history-list">
              {locationHistory.map((update, index) => (
                <div key={update.id || index} className="history-item">
                  <span className="time">
                    {new Date(update.timestamp).toLocaleTimeString()}
                  </span>
                  <span className="coords">
                    {update.coordinates[1].toFixed(4)}, {update.coordinates[0].toFixed(4)}
                  </span>
                  <span className="accuracy">±{Math.round(update.accuracy)}m</span>
                </div>
              ))}
            </div>
          </div>
        )}

        <h3>Alert Settings</h3>
        
        <div className="setting-item">
          <label>
            <input type="checkbox" defaultChecked />
            Vibration alerts
          </label>
        </div>

        <div className="setting-item">
          <label>
            <input type="checkbox" defaultChecked />
            Sound alerts
          </label>
        </div>

        <div className="setting-item">
          <label>
            <input type="checkbox" defaultChecked />
            Background tracking
          </label>
        </div>
      </div>

      <div className="info-section">
        <h3>How it works</h3>
        <ul>
          <li>GPS tracks your location continuously</li>
          <li>Alerts trigger when entering protected zones</li>
          <li>Works offline with downloaded data</li>
          <li>Breach events sync when back online</li>
        </ul>
      </div>
    </div>
  );
};

export default LocationTracker;

/* Mapbox CSS imports */
@import 'mapbox-gl/dist/mapbox-gl.css';
@import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

/* Global CSS Reset and Variables */
:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color scheme for coastal marine theme */
  --primary-blue: #0066cc;
  --secondary-blue: #004499;
  --accent-teal: #20b2aa;
  --warning-orange: #ff6b35;
  --danger-red: #e74c3c;
  --success-green: #27ae60;
  
  /* Neutral colors */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;
  --black: #000000;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-lg: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);

  color-scheme: light;
  color: var(--dark-gray);
  background-color: var(--light-gray);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--secondary-blue);
}

/* Base element styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Button styles */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--secondary-blue);
}

.btn-secondary {
  background-color: var(--medium-gray);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--dark-gray);
}

.btn-success {
  background-color: var(--success-green);
  color: var(--white);
}

.btn-warning {
  background-color: var(--warning-orange);
  color: var(--white);
}

.btn-danger {
  background-color: var(--danger-red);
  color: var(--white);
}

/* Form styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--dark-gray);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.25);
}

/* Card styles */
.card {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  background-color: var(--light-gray);
}

.card-body {
  padding: var(--spacing-md);
}

/* Alert styles */
.alert {
  padding: var(--spacing-md);
  border-radius: 4px;
  margin-bottom: var(--spacing-md);
}

.alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* Utility classes */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

# Coastal Marine Protection PWA

A Progressive Web App for coastal marine protection that allows authorities to manage protected marine zones and provides community users with offline geofencing alerts.

## Features

### Authority Interface
- **Zone Management**: Draw, edit, and delete protected marine zones on interactive maps
- **Real-time Monitoring**: Monitor zone breaches with live notifications
- **Analytics Dashboard**: View breach statistics and zone effectiveness

### Community Interface
- **Offline Navigation**: Pre-download map tiles and zone data for offline use
- **Geofencing Alerts**: Real-time alerts when entering protected zones
- **Location Tracking**: Background location monitoring with privacy controls

### PWA Capabilities
- **Offline-First**: Works without internet connection using cached data
- **Installable**: Can be installed on mobile devices and desktops
- **Background Sync**: Syncs breach events when connection is restored
- **Push Notifications**: Real-time alerts for authorities and users

## Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Mapping**: Mapbox GL JS + Mapbox GL Draw
- **Backend**: Supabase (PostgreSQL + PostGIS, Auth, Realtime)
- **Offline**: Workbox Service Worker + IndexedDB (localForage)
- **Geospatial**: Turf.js for spatial calculations
- **PWA**: Vite PWA plugin with advanced caching strategies

## Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account
- Mapbox account (for maps and tiles)

### Installation

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd coastal
npm install
```

2. **Environment Setup**:
```bash
cp .env.example .env
```

3. **Configure environment variables** in `.env`:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
VITE_PUSH_VAPID_KEY=your_vapid_public_key
```

4. **Set up Supabase database**:
   - Create a new Supabase project
   - Run the SQL script from `database/schema.sql` in the Supabase SQL editor
   - This creates tables with PostGIS for geospatial data and Row Level Security

5. **Start development server**:
```bash
npm run dev
```

The app will be available at `http://localhost:3001`

## Database Setup

The app uses Supabase with PostGIS extension for geospatial operations. Run the provided schema:

```sql
-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;

-- Run the complete schema from database/schema.sql
```

### Key Tables
- `profiles`: User profiles with roles (authority/community)
- `protected_zones`: Marine protection zones with PostGIS geometry
- `breach_events`: Location breach tracking with spatial indexing
- `download_regions`: Offline map download regions

## Development

### Project Structure
```
src/
├── components/           # React components
│   ├── authority/       # Authority-specific components
│   ├── community/       # Community user components
│   ├── common/          # Shared components
│   └── map/             # Map-related components
├── pages/               # Main page components
├── services/            # Business logic and API calls
├── store/               # React Context state management
├── types/               # TypeScript type definitions
└── css/                 # Component-specific styles
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Key Services
- **Supabase Service**: Database operations and authentication
- **Offline Storage**: localForage wrapper for IndexedDB
- **Geofencing Service**: Turf.js spatial calculations
- **Service Worker**: Background sync and push notifications

## PWA Features

### Offline Functionality
- Map tiles cached using Workbox strategies
- Zone data stored in IndexedDB for offline access
- Background sync queues breach events when offline

### Installation
The app can be installed on:
- Mobile devices (iOS Safari, Android Chrome)
- Desktop browsers (Chrome, Edge, Safari)
- Provides native app-like experience

### Performance Targets
- Map render: ≤100ms
- Geofence check: ≤50ms  
- Offline cache hit rate: ≥98%
- Download region: ≤30s over 4G

## Deployment

### Build for Production
```bash
npm run build
```

### Environment Variables for Production
Set these in your deployment platform:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY` 
- `VITE_MAPBOX_ACCESS_TOKEN`
- `VITE_PUSH_VAPID_KEY`

### Deployment Platforms
- **Vercel**: Optimal for React + Supabase apps
- **Netlify**: Good PWA support with edge functions
- **Supabase**: Direct deployment to Supabase platform

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make changes following the coding guidelines
4. Test thoroughly including offline scenarios
5. Submit a pull request

### Coding Guidelines
- Use TypeScript strict mode
- Follow React best practices (hooks, functional components)
- Implement proper error handling for offline scenarios
- Include JSDoc comments for complex functions
- Use CSS modules for component styling

## License

[MIT License](LICENSE)

## Security

- HTTPS only in production
- Supabase Row-Level Security for data access
- API keys secured via environment variables
- GeoJSON input sanitization
- Privacy-focused location tracking

## Support

For issues and questions:
- Check the [Issues](../../issues) page
- Review the [Wiki](../../wiki) for detailed documentation
- Contact the development team
  },
})
```

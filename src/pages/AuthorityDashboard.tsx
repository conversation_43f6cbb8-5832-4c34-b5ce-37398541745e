import React, { useState } from 'react';
import { useApp } from '../store/AppContext';
import { MapboxMap } from '../components/map/MapboxMap';
import { ZoneManager } from '../components/authority/ZoneManager';
import { BreachMonitor } from '../components/authority/BreachMonitor';
import ErrorBoundary from '../components/common/ErrorBoundary';
import NetworkStatus from '../components/common/NetworkStatus';
import type { ProtectedZone } from '../types';
import { useErrorHandler } from '../hooks/useErrorHandler';
import './AuthorityDashboard.css';

export const AuthorityDashboard: React.FC = () => {
  const { state, signOut } = useApp();
  const { handleError } = useErrorHandler();
  const [activeTab, setActiveTab] = useState<'zones' | 'breaches' | 'map'>('map');
  const [zones, setZones] = useState<ProtectedZone[]>([]);
  const [selectedZone, setSelectedZone] = useState<ProtectedZone | null>(null);

  const handleZoneCreated = (zone: ProtectedZone) => {
    setZones(prev => [...prev, zone]);
  };

  const handleZoneUpdated = (updatedZone: ProtectedZone) => {
    setZones(prev => prev.map(z => z.id === updatedZone.id ? updatedZone : z));
  };

  const handleZoneDeleted = (zoneId: string) => {
    setZones(prev => prev.filter(z => z.id !== zoneId));
    if (selectedZone?.id === zoneId) {
      setSelectedZone(null);
    }
  };

  return (
    <div className="authority-dashboard">
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>🌊 Coastal Guard - Authority</h1>
            <span className="user-info">
              {state.user?.profile?.name || state.user?.email}
            </span>
          </div>
          <div className="header-right">
            <NetworkStatus variant="header" />
            <button onClick={signOut} className="sign-out-btn">
              Sign Out
            </button>
          </div>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button 
          className={activeTab === 'map' ? 'active' : ''}
          onClick={() => setActiveTab('map')}
        >
          🗺️ Map View
        </button>
        <button 
          className={activeTab === 'zones' ? 'active' : ''}
          onClick={() => setActiveTab('zones')}
        >
          🚫 Manage Zones
        </button>
        <button 
          className={activeTab === 'breaches' ? 'active' : ''}
          onClick={() => setActiveTab('breaches')}
        >
          ⚠️ Breach Monitor
        </button>
      </nav>

      <main className="dashboard-content">
        {activeTab === 'map' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Map View')}
            fallback={<div className="error-fallback">Map failed to load. Please refresh the page.</div>}
          >
            <div className="map-tab">
              <MapboxMap
                zones={zones}
                selectedZone={selectedZone}
                onZoneSelect={setSelectedZone}
                userRole="authority"
                onZoneCreated={handleZoneCreated}
                onZoneUpdated={handleZoneUpdated}
                onZoneDeleted={handleZoneDeleted}
              />
            </div>
          </ErrorBoundary>
        )}

        {activeTab === 'zones' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Zone Manager')}
            fallback={<div className="error-fallback">Zone management failed to load. Please try again.</div>}
          >
            <div className="zones-tab">
              <ZoneManager />
            </div>
          </ErrorBoundary>
        )}

        {activeTab === 'breaches' && (
          <ErrorBoundary 
            onError={(error) => handleError(error, 'Breach Monitor')}
            fallback={<div className="error-fallback">Breach monitoring failed to load. Please try again.</div>}
          >
            <div className="breaches-tab">
              <BreachMonitor />
            </div>
          </ErrorBoundary>
        )}
      </main>
    </div>
  );
};

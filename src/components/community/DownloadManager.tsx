import React, { useState, useEffect, useCallback } from 'react';
import { OfflineStorageService } from '../../services/offlineStorage';
import { MapTileDownloadService, type DownloadProgress as TileDownloadProgress } from '../../services/mapTileDownload';
import { supabase } from '../../services/supabase';
import type { DownloadRegion, ProtectedZone } from '../../types';
import LoadingSpinner from '../common/LoadingSpinner';
import './DownloadManager.css';

const DownloadManager: React.FC = () => {
  const [regions, setRegions] = useState<DownloadRegion[]>([]);
  const [availableZones, setAvailableZones] = useState<ProtectedZone[]>([]);
  const [downloadProgress, setDownloadProgress] = useState<TileDownloadProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newRegion, setNewRegion] = useState({
    name: '',
    selectedZones: [] as string[],
  });
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load saved download regions
      const savedRegions = await OfflineStorageService.getDownloadedRegions();
      setRegions(savedRegions);

      // Load available protected zones
      const { data: zonesData, error: zonesError } = await supabase
        .from('protected_zones')
        .select(`
          id,
          name,
          description,
          geometry,
          effective_from,
          effective_to,
          enforcement_notes,
          created_by,
          created_at,
          updated_at
        `)
        .eq('effective_to', null) // Only active zones
        .order('name');

      if (zonesError) throw zonesError;

      // Transform database zones to ProtectedZone format
      const transformedZones: ProtectedZone[] = (zonesData || []).map(zone => ({
        id: zone.id,
        type: 'Feature' as const,
        geometry: zone.geometry,
        properties: {
          name: zone.name,
          description: zone.description,
          effective_from: zone.effective_from,
          effective_to: zone.effective_to,
          enforcement_notes: zone.enforcement_notes,
          created_by: zone.created_by,
          created_at: zone.created_at,
          updated_at: zone.updated_at
        },
        // Legacy properties for backward compatibility
        name: zone.name,
        description: zone.description,
        coordinates: zone.geometry?.coordinates || [],
        isActive: !zone.effective_to
      }));

      setAvailableZones(transformedZones);

    } catch (err) {
      console.error('Error loading download data:', err);
      setError('Failed to load download data');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRegionBounds = (zoneIds: string[]): DownloadRegion['bounds'] | null => {
    if (zoneIds.length === 0) return null;

    const selectedZones = availableZones.filter(zone => zoneIds.includes(zone.id));
    if (selectedZones.length === 0) return null;

    let minLng = Infinity, minLat = Infinity;
    let maxLng = -Infinity, maxLat = -Infinity;

    selectedZones.forEach(zone => {
      if (zone.geometry && zone.geometry.type === 'Polygon') {
        zone.geometry.coordinates[0].forEach((position) => {
          const [lng, lat] = position;
          minLng = Math.min(minLng, lng);
          maxLng = Math.max(maxLng, lng);
          minLat = Math.min(minLat, lat);
          maxLat = Math.max(maxLat, lat);
        });
      }
    });

    // Add padding around the bounds
    const padding = 0.01; // ~1km
    return {
      north: maxLat + padding,
      south: minLat - padding,
      east: maxLng + padding,
      west: minLng - padding
    };
  };

  const handleDownloadProgress = useCallback((progress: TileDownloadProgress) => {
    setDownloadProgress(progress);
  }, []);

  const downloadRegion = async (region: DownloadRegion) => {
    try {
      setError(null);
      setDownloadProgress({
        regionId: region.id,
        totalTiles: 0,
        downloadedTiles: 0,
        failedTiles: 0,
        speed: 0,
        estimatedTimeRemaining: 0,
        bytesDownloaded: 0,
        bytesPerSecond: 0,
        currentTile: undefined
      });

      console.log(`Starting download for region: ${region.name}`);

      // Download using MapTileDownloadService
      const success = await MapTileDownloadService.downloadRegion(
        region,
        'mapbox://styles/mapbox/streets-v12',
        handleDownloadProgress
      );

      if (success) {
        console.log('Download completed successfully');
        await loadData(); // Refresh the regions list
      } else {
        setError('Download failed or was cancelled');
      }

    } catch (err) {
      console.error('Error downloading region:', err);
      setError('Failed to download region');
    } finally {
      setDownloadProgress(null);
    }
  };

  const deleteRegion = async (regionId: string) => {
    if (!window.confirm('Are you sure you want to delete this downloaded region?')) {
      return;
    }

    try {
      await OfflineStorageService.deleteRegion(regionId);
      await loadData();
    } catch (err) {
      console.error('Error deleting region:', err);
      setError('Failed to delete region');
    }
  };

  const createRegion = async () => {
    try {
      setError(null);

      if (!newRegion.name.trim()) {
        setError('Region name is required');
        return;
      }

      if (newRegion.selectedZones.length === 0) {
        setError('Please select at least one zone');
        return;
      }

      const bounds = calculateRegionBounds(newRegion.selectedZones);
      if (!bounds) {
        setError('Could not calculate bounds for selected zones');
        return;
      }

      const region: DownloadRegion = {
        id: `region_${Date.now()}`,
        name: newRegion.name.trim(),
        bounds,
        zoomLevels: {
          min: 8,
          max: 14
        }
      };

      await OfflineStorageService.storeRegion(region);
      await loadData();

      // Reset form
      setNewRegion({
        name: '',
        selectedZones: [],
      });
      setIsCreating(false);

    } catch (err) {
      console.error('Error creating region:', err);
      setError('Failed to create download region');
    }
  };

  const formatSize = (bytes: number | undefined): string => {
    if (!bytes) return '0 KB';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getDownloadEstimate = (region: DownloadRegion): { tileCount: number; estimatedSizeMB: number } => {
    return MapTileDownloadService.estimateDownloadSize(region);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const isDownloading = downloadProgress !== null;
  const activeDownloads = MapTileDownloadService.getActiveDownloads();

  return (
    <div className="download-manager">
      <div className="download-manager-header">
        <h2>Offline Downloads</h2>
        {!isCreating && (
          <button 
            className="btn btn-primary"
            onClick={() => setIsCreating(true)}
            disabled={isDownloading}
          >
            Create Download Region
          </button>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {downloadProgress && (
        <div className="download-progress">
          <h3>Downloading Region...</h3>
          <div className="progress-info">
            <div className="progress-stats">
              <span>
                {downloadProgress.downloadedTiles} / {downloadProgress.totalTiles} tiles
              </span>
              <span>
                {formatSize(downloadProgress.bytesDownloaded)}
              </span>
              {downloadProgress.speed > 0 && (
                <span>
                  {downloadProgress.speed.toFixed(1)} tiles/s
                </span>
              )}
              {downloadProgress.estimatedTimeRemaining > 0 && (
                <span>
                  ETA: {formatTime(downloadProgress.estimatedTimeRemaining)}
                </span>
              )}
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ 
                  width: `${Math.round((downloadProgress.downloadedTiles / downloadProgress.totalTiles) * 100)}%` 
                }}
              />
            </div>
            <div className="progress-percentage">
              {Math.round((downloadProgress.downloadedTiles / downloadProgress.totalTiles) * 100)}% complete
            </div>
          </div>
          {downloadProgress.currentTile && (
            <div className="current-tile">
              Downloading: {downloadProgress.currentTile.z}/{downloadProgress.currentTile.x}/{downloadProgress.currentTile.y}
            </div>
          )}
        </div>
      )}

      {isCreating && (
        <div className="create-region-form">
          <h3>Create Download Region</h3>
          
          <div className="form-group">
            <label htmlFor="region-name">Region Name *</label>
            <input
              id="region-name"
              type="text"
              value={newRegion.name}
              onChange={(e) => setNewRegion({ ...newRegion, name: e.target.value })}
              placeholder="Enter region name"
              required
            />
          </div>

          <div className="form-group">
            <label>Select Zones to Include *</label>
            <div className="zone-selection">
              {availableZones.map(zone => (
                <label key={zone.id} className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRegion.selectedZones.includes(zone.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewRegion({
                          ...newRegion,
                          selectedZones: [...newRegion.selectedZones, zone.id]
                        });
                      } else {
                        setNewRegion({
                          ...newRegion,
                          selectedZones: newRegion.selectedZones.filter(id => id !== zone.id)
                        });
                      }
                    }}
                  />
                  {zone.properties?.name || zone.name}
                </label>
              ))}
            </div>
          </div>

          <div className="form-actions">
            <button 
              className="btn btn-secondary"
              onClick={() => {
                setIsCreating(false);
                setNewRegion({ name: '', selectedZones: [] });
                setError(null);
              }}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={createRegion}
            >
              Create Region
            </button>
          </div>
        </div>
      )}

      <div className="regions-list">
        <h3>Download Regions ({regions.length})</h3>
        
        {regions.length === 0 ? (
          <div className="empty-state">
            <p>No download regions created yet.</p>
            <button 
              className="btn btn-primary"
              onClick={() => setIsCreating(true)}
            >
              Create Your First Region
            </button>
          </div>
        ) : (
          <div className="regions">
            {regions.map(region => {
              const estimate = getDownloadEstimate(region);
              const isRegionDownloading = activeDownloads.includes(region.id);
              const isRegionDownloaded = !!region.downloadedAt;
              
              return (
                <div key={region.id} className="region-item">
                  <div className="region-info">
                    <h4>{region.name}</h4>
                    <div className="region-meta">
                      <span className={`download-status ${isRegionDownloaded ? 'downloaded' : 'pending'}`}>
                        {isRegionDownloaded ? '✓ Downloaded' : '⏳ Pending'}
                      </span>
                      {region.size && (
                        <span className="region-size">
                          {formatSize(region.size)}
                        </span>
                      )}
                    </div>
                    <div className="region-details">
                      <span>Zoom: {region.zoomLevels.min}-{region.zoomLevels.max}</span>
                      <span>~{estimate.tileCount} tiles (~{estimate.estimatedSizeMB} MB)</span>
                    </div>
                    {region.downloadedAt && (
                      <div className="download-date">
                        Downloaded: {new Date(region.downloadedAt).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  
                  <div className="region-actions">
                    {!isRegionDownloaded ? (
                      <button 
                        className="btn btn-primary"
                        onClick={() => downloadRegion(region)}
                        disabled={isDownloading || isRegionDownloading}
                      >
                        {isRegionDownloading ? 'Downloading...' : 'Download'}
                      </button>
                    ) : (
                      <button 
                        className="btn btn-secondary"
                        onClick={() => downloadRegion(region)}
                        disabled={isDownloading || isRegionDownloading}
                      >
                        {isRegionDownloading ? 'Updating...' : 'Update'}
                      </button>
                    )}
                    <button 
                      className="btn btn-danger"
                      onClick={() => deleteRegion(region.id)}
                      disabled={isDownloading || isRegionDownloading}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {regions.length > 0 && (
        <div className="download-stats">
          <h3>Storage Usage</h3>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">Total Regions:</span>
              <span className="stat-value">{regions.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Downloaded:</span>
              <span className="stat-value">
                {regions.filter(r => r.downloadedAt).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Total Size:</span>
              <span className="stat-value">
                {formatSize(regions.reduce((sum, r) => sum + (r.size || 0), 0))}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DownloadManager;



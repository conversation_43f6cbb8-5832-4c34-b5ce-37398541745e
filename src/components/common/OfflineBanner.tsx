import React from 'react';
import './OfflineBanner.css';

interface OfflineBannerProps {
  isOnline: boolean;
  showBanner?: boolean;
}

const OfflineBanner: React.FC<OfflineBannerProps> = ({ 
  isOnline, 
  showBanner = true 
}) => {
  if (!showBanner) return null;

  if (!isOnline) {
    return (
      <div className="offline-banner" role="alert" aria-live="polite">
        <div className="offline-banner-content">
          <span className="status-icon">🔴</span>
          <span>You're offline - Some features may be limited</span>
          <span className="cached-note">Using cached data</span>
        </div>
      </div>
    );
  }

  return null;
};

export default OfflineBanner;

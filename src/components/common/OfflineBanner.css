/* Offline Banner Styles */
.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: center;
  z-index: 9999;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
  animation: slideDown 0.3s ease-out;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.offline-banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.offline-banner .status-icon {
  font-size: 1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.cached-note {
  font-size: 0.75rem;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Ensure content doesn't overlap with banner */
.offline-banner + * {
  margin-top: 48px; /* Adjust based on banner height */
}

/* Online reconnection banner */
.online-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #059669, #047857);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: center;
  z-index: 9999;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
  animation: slideDown 0.3s ease-out, slideUp 0.3s ease-in 2.7s forwards;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.online-banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  max-width: 1200px;
  margin: 0 auto;
}

@keyframes slideUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .offline-banner,
  .online-banner {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .offline-banner-content,
  .online-banner-content {
    gap: var(--spacing-xs);
  }

  .cached-note {
    font-size: 0.7rem;
    padding: 0.1rem 0.4rem;
  }

  .offline-banner + * {
    margin-top: 40px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .offline-banner,
  .online-banner {
    animation: none;
  }

  @keyframes slideDown {
    from, to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from, to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .offline-banner {
    background: #cc0000;
    border-bottom: 2px solid #ffffff;
  }

  .online-banner {
    background: #008800;
    border-bottom: 2px solid #ffffff;
  }

  .cached-note {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid #ffffff;
  }
}

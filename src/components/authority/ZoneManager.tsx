import React, { useState, useEffect } from 'react';
import { supabase } from '../../services/supabase';
import { useAppContext } from '../../store/AppContext';
import type { ProtectedZone } from '../../types';
import LoadingSpinner from '../common/LoadingSpinner';
import './ZoneManager.css';

interface ZoneFormData {
  name: string;
  description: string;
  protection_level: 'no_take' | 'restricted' | 'seasonal';
  regulations: string;
  is_active: boolean;
}

const ZoneManager: React.FC = () => {
  const { user } = useAppContext();
  const [zones, setZones] = useState<ProtectedZone[]>([]);
  const [selectedZone, setSelectedZone] = useState<ProtectedZone | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<ZoneFormData>({
    name: '',
    description: '',
    protection_level: 'no_take',
    regulations: '',
    is_active: true
  });

  useEffect(() => {
    loadZones();
  }, []);

  const loadZones = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('protected_zones')
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;

      setZones(data || []);
    } catch (err) {
      console.error('Error loading zones:', err);
      setError('Failed to load protected zones');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateZone = () => {
    setIsCreating(true);
    setSelectedZone(null);
    setFormData({
      name: '',
      description: '',
      protection_level: 'no_take',
      regulations: '',
      is_active: true
    });
  };

  const handleEditZone = (zone: ProtectedZone) => {
    setSelectedZone(zone);
    setIsCreating(false);
    setFormData({
      name: zone.name,
      description: zone.description || '',
      protection_level: zone.protection_level || 'restricted',
      regulations: zone.regulations || '',
      is_active: zone.is_active ?? true
    });
  };

  const handleSaveZone = async () => {
    try {
      setError(null);

      if (!formData.name.trim()) {
        setError('Zone name is required');
        return;
      }

      // Note: Geometry will be set via map drawing interface
      // For now, we'll use a placeholder polygon
      const placeholderGeometry = {
        type: 'Polygon' as const,
        coordinates: [[
          [-120.0, 34.0],
          [-120.1, 34.0], 
          [-120.1, 34.1],
          [-120.0, 34.1],
          [-120.0, 34.0]
        ]]
      };

      const zoneData = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        protection_level: formData.protection_level,
        regulations: formData.regulations.trim() || null,
        is_active: formData.is_active,
        geometry: placeholderGeometry,
        created_by: user?.id
      };

      if (selectedZone) {
        // Update existing zone
        const { error: updateError } = await supabase
          .from('protected_zones')
          .update(zoneData)
          .eq('id', selectedZone.id);

        if (updateError) throw updateError;
      } else {
        // Create new zone
        const { error: insertError } = await supabase
          .from('protected_zones')
          .insert([zoneData]);

        if (insertError) throw insertError;
      }

      await loadZones();
      setIsCreating(false);
      setSelectedZone(null);
    } catch (err) {
      console.error('Error saving zone:', err);
      setError('Failed to save zone');
    }
  };

  const handleDeleteZone = async (zoneId: string) => {
    if (!window.confirm('Are you sure you want to delete this zone? This action cannot be undone.')) {
      return;
    }

    try {
      setError(null);

      const { error: deleteError } = await supabase
        .from('protected_zones')
        .delete()
        .eq('id', zoneId);

      if (deleteError) throw deleteError;

      await loadZones();
    } catch (err) {
      console.error('Error deleting zone:', err);
      setError('Failed to delete zone');
    }
  };

  const handleCancel = () => {
    setIsCreating(false);
    setSelectedZone(null);
    setError(null);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="zone-manager">
      <div className="zone-manager-header">
        <h2>Protected Zones</h2>
        {!isCreating && !selectedZone && (
          <button 
            className="btn btn-primary"
            onClick={handleCreateZone}
          >
            Create New Zone
          </button>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {(isCreating || selectedZone) && (
        <div className="zone-form">
          <h3>{selectedZone ? 'Edit Zone' : 'Create New Zone'}</h3>
          
          <div className="form-group">
            <label htmlFor="zone-name">Zone Name *</label>
            <input
              id="zone-name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter zone name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="zone-description">Description</label>
            <textarea
              id="zone-description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Describe the protected zone"
              rows={3}
            />
          </div>

          <div className="form-group">
            <label htmlFor="protection-level">Protection Level</label>
            <select
              id="protection-level"
              value={formData.protection_level}
              onChange={(e) => setFormData({ ...formData, protection_level: e.target.value as any })}
            >
              <option value="no_take">No Take Zone</option>
              <option value="restricted">Restricted Access</option>
              <option value="seasonal">Seasonal Protection</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="regulations">Regulations</label>
            <textarea
              id="regulations"
              value={formData.regulations}
              onChange={(e) => setFormData({ ...formData, regulations: e.target.value })}
              placeholder="List specific regulations and restrictions"
              rows={4}
            />
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              />
              Zone is active
            </label>
          </div>

          <div className="form-actions">
            <button 
              className="btn btn-secondary"
              onClick={handleCancel}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleSaveZone}
            >
              {selectedZone ? 'Update Zone' : 'Create Zone'}
            </button>
          </div>
        </div>
      )}

      <div className="zone-list">
        {zones.map(zone => (
          <div key={zone.id} className="zone-item">
            <div className="zone-info">
              <h3>{zone.name}</h3>
              {zone.description && <p>{zone.description}</p>}
              <div className="zone-meta">
                <span className={`zone-status ${zone.is_active ? 'active' : 'inactive'}`}>
                  {zone.is_active ? 'Active' : 'Inactive'}
                </span>
                <span className="protection-level">
                  {zone.protection_level?.replace('_', ' ').toUpperCase() || 'RESTRICTED'}
                </span>
              </div>
              {zone.regulations && (
                <div className="zone-regulations">
                  <strong>Regulations:</strong> {zone.regulations}
                </div>
              )}
            </div>
            <div className="zone-actions">
              <button 
                className="btn btn-secondary"
                onClick={() => handleEditZone(zone)}
              >
                Edit
              </button>
              <button 
                className="btn btn-danger"
                onClick={() => handleDeleteZone(zone.id)}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {zones.length === 0 && !isCreating && (
        <div className="empty-state">
          <p>No protected zones created yet.</p>
          <button 
            className="btn btn-primary"
            onClick={handleCreateZone}
          >
            Create Your First Zone
          </button>
        </div>
      )}
    </div>
  );
};

export default ZoneManager;
export { ZoneManager };

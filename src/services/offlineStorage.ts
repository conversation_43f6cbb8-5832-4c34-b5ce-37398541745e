import localforage from 'localforage';
import type { DownloadRegion, CachedTile, SyncQueue, BreachEvent } from '../types';
import type { LocationUpdate } from './backgroundLocation';

// Configure localforage for different data types
const tileStorage = localforage.createInstance({
  name: 'CoastalGuard',
  storeName: 'tiles'
});

const regionStorage = localforage.createInstance({
  name: 'CoastalGuard',
  storeName: 'regions'
});

const syncStorage = localforage.createInstance({
  name: 'CoastalGuard',
  storeName: 'sync'
});

const zoneStorage = localforage.createInstance({
  name: 'CoastalGuard',
  storeName: 'zones'
});

const locationStorage = localforage.createInstance({
  name: 'CoastalGuard',
  storeName: 'locations'
});

export class OfflineStorageService {
  /**
   * Cache a map tile for offline use
   */
  static async cacheTile(url: string, data: ArrayBuffer): Promise<void> {
    const cachedTile: CachedTile = {
      url,
      data,
      timestamp: Date.now()
    };
    await tileStorage.setItem(url, cachedTile);
  }

  /**
   * Retrieve a cached tile
   */
  static async getCachedTile(url: string): Promise<CachedTile | null> {
    try {
      return await tileStorage.getItem<CachedTile>(url);
    } catch {
      return null;
    }
  }

  /**
   * Store downloaded region metadata
   */
  static async storeRegion(region: DownloadRegion): Promise<void> {
    await regionStorage.setItem(region.id, region);
  }

  /**
   * Get all downloaded regions
   */
  static async getDownloadedRegions(): Promise<DownloadRegion[]> {
    const regions: DownloadRegion[] = [];
    await regionStorage.iterate<DownloadRegion, void>((region) => {
      regions.push(region);
    });
    return regions;
  }

  /**
   * Delete a downloaded region and its tiles
   */
  static async deleteRegion(regionId: string): Promise<void> {
    await regionStorage.removeItem(regionId);
    // Note: In a production app, you'd also clean up associated tiles
  }

  /**
   * Store zones for offline access
   */
  static async storeZones(zones: any[]): Promise<void> {
    await zoneStorage.setItem('protected_zones', zones);
  }

  /**
   * Get cached zones
   */
  static async getCachedZones(): Promise<any[]> {
    try {
      return await zoneStorage.getItem<any[]>('protected_zones') || [];
    } catch {
      return [];
    }
  }

  /**
   * Add breach event to sync queue
   */
  static async queueBreachEvent(event: BreachEvent): Promise<void> {
    const queue = await this.getSyncQueue();
    queue.breachEvents.push(event);
    await syncStorage.setItem('breach_queue', queue);
  }

  /**
   * Get sync queue
   */
  static async getSyncQueue(): Promise<SyncQueue> {
    try {
      return await syncStorage.getItem<SyncQueue>('breach_queue') || {
        breachEvents: []
      };
    } catch {
      return { breachEvents: [] };
    }
  }

  /**
   * Clear synced breach events from queue
   */
  static async clearSyncedEvents(syncedEventIds: string[]): Promise<void> {
    const queue = await this.getSyncQueue();
    queue.breachEvents = queue.breachEvents.filter(
      event => !event.id || !syncedEventIds.includes(event.id)
    );
    queue.lastSyncAttempt = new Date().toISOString();
    await syncStorage.setItem('breach_queue', queue);
  }

  /**
   * Get storage usage statistics
   */
  static async getStorageStats(): Promise<{
    tileCount: number;
    regionCount: number;
    queuedEvents: number;
    locationCount: number;
    estimatedSize: number;
  }> {
    const tileCount = await tileStorage.length();
    const regionCount = await regionStorage.length();
    const locationCount = await locationStorage.length();
    const queue = await this.getSyncQueue();
    
    return {
      tileCount,
      regionCount,
      queuedEvents: queue.breachEvents.length,
      locationCount,
      estimatedSize: 0 // Would need to calculate actual sizes
    };
  }

  /**
   * Store location updates for offline access and sync
   */
  static async storeLocationUpdates(updates: LocationUpdate[]): Promise<void> {
    await locationStorage.setItem('location_updates', updates);
  }

  /**
   * Get stored location updates
   */
  static async getLocationUpdates(): Promise<LocationUpdate[]> {
    try {
      return await locationStorage.getItem<LocationUpdate[]>('location_updates') || [];
    } catch {
      return [];
    }
  }

  /**
   * Clear all location update history
   */
  static async clearLocationUpdates(): Promise<void> {
    await locationStorage.clear();
  }

  /**
   * Add location update to background sync queue
   */
  static async queueLocationUpdate(update: LocationUpdate): Promise<void> {
    const queue = await syncStorage.getItem<{ locationUpdates: LocationUpdate[] }>('location_queue') || { locationUpdates: [] };
    queue.locationUpdates.push(update);
    await syncStorage.setItem('location_queue', queue);
  }

  /**
   * Get location update sync queue
   */
  static async getLocationSyncQueue(): Promise<{ locationUpdates: LocationUpdate[] }> {
    try {
      return await syncStorage.getItem<{ locationUpdates: LocationUpdate[] }>('location_queue') || { locationUpdates: [] };
    } catch {
      return { locationUpdates: [] };
    }
  }

  /**
   * Clear synced location updates from queue
   */
  static async clearSyncedLocationUpdates(syncedUpdateIds: string[]): Promise<void> {
    const queue = await this.getLocationSyncQueue();
    queue.locationUpdates = queue.locationUpdates.filter(
      update => !syncedUpdateIds.includes(update.id)
    );
    await syncStorage.setItem('location_queue', queue);
  }
}

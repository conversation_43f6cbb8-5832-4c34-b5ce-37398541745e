import { useState, useCallback } from 'react';

interface ErrorState {
  error: Error | null;
  hasError: boolean;
  errorId: string | null;
}

interface UseErrorHandlerReturn {
  error: Error | null;
  hasError: boolean;
  errorId: string | null;
  handleError: (error: Error, context?: string) => void;
  clearError: () => void;
  retryAction: (action: () => Promise<void> | void) => Promise<void>;
}

/**
 * Custom hook for handling errors consistently across the application
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    hasError: false,
    errorId: null
  });

  const handleError = useCallback((error: Error, context?: string) => {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log error with context
    console.error(`Error${context ? ` in ${context}` : ''}:`, error);

    // Set error state
    setErrorState({
      error,
      hasError: true,
      errorId
    });

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // reportError(error, { context, errorId, timestamp: new Date().toISOString() });
    }

    // For certain types of errors, you might want to show different handling
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      // Handle network errors differently
      console.log('Network error detected, might be offline');
    }
  }, []);

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      hasError: false,
      errorId: null
    });
  }, []);

  const retryAction = useCallback(async (action: () => Promise<void> | void) => {
    try {
      clearError();
      await action();
    } catch (error) {
      handleError(error as Error, 'retry');
    }
  }, [handleError, clearError]);

  return {
    error: errorState.error,
    hasError: errorState.hasError,
    errorId: errorState.errorId,
    handleError,
    clearError,
    retryAction
  };
};

/**
 * Helper function to create error-safe async operations
 */
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  onError: (error: Error) => void,
  context?: string
) => {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      onError(error as Error);
      console.error(`Error in ${context || 'async operation'}:`, error);
      return undefined;
    }
  };
};

/**
 * Helper function to determine if an error is recoverable
 */
export const isRecoverableError = (error: Error): boolean => {
  // Network errors are usually recoverable
  if (error.name === 'NetworkError' || error.message.includes('fetch')) {
    return true;
  }

  // Timeout errors are recoverable
  if (error.message.includes('timeout')) {
    return true;
  }

  // Supabase errors that are retryable
  if (error.message.includes('PGRST') || error.message.includes('connection')) {
    return true;
  }

  // Generic server errors (5xx) are usually recoverable
  if (error.message.includes('Internal Server Error') || error.message.includes('502') || error.message.includes('503')) {
    return true;
  }

  return false;
};

/**
 * Helper function to get user-friendly error messages
 */
export const getUserFriendlyErrorMessage = (error: Error): string => {
  // Network-related errors
  if (error.name === 'NetworkError' || error.message.includes('Failed to fetch')) {
    return 'Network connection problem. Please check your internet connection and try again.';
  }

  // Timeout errors
  if (error.message.includes('timeout')) {
    return 'The request took too long to complete. Please try again.';
  }

  // Authentication errors
  if (error.message.includes('unauthorized') || error.message.includes('401')) {
    return 'Your session has expired. Please sign in again.';
  }

  // Permission errors
  if (error.message.includes('forbidden') || error.message.includes('403')) {
    return 'You do not have permission to perform this action.';
  }

  // Not found errors
  if (error.message.includes('not found') || error.message.includes('404')) {
    return 'The requested resource was not found.';
  }

  // Server errors
  if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
    return 'A server error occurred. Please try again later.';
  }

  // Geolocation errors
  if (error.message.includes('geolocation') || error.message.includes('location')) {
    return 'Location access is required for this feature. Please enable location services.';
  }

  // Storage errors
  if (error.message.includes('storage') || error.message.includes('quota')) {
    return 'Storage limit reached. Please clear some space and try again.';
  }

  // Default to the original error message (but sanitized)
  return error.message || 'An unexpected error occurred. Please try again.';
};

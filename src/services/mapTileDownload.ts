/**
 * Map Tile Download Service
 * Handles downloading and caching of Mapbox map tiles for offline use
 */

import type { DownloadRegion, CachedTile } from '../types';
import { OfflineStorageService } from './offlineStorage';

export interface TileCoordinate {
  x: number;
  y: number;
  z: number;
}

export interface DownloadProgress {
  regionId: string;
  totalTiles: number;
  downloadedTiles: number;
  failedTiles: number;
  currentTile?: TileCoordinate;
  speed: number; // tiles per second
  estimatedTimeRemaining: number; // seconds
  bytesDownloaded: number;
  bytesPerSecond: number;
}

export interface MapStyle {
  id: string;
  name: string;
  url: string;
}

export class MapTileDownloadService {
  private static mapboxAccessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;
  // private static readonly MAPBOX_BASE_URL = 'https://api.mapbox.com/styles/v1';
  private static readonly MAPBOX_TILES_URL = 'https://api.mapbox.com/v4';
  
  // Default map styles
  private static readonly MAP_STYLES: MapStyle[] = [
    {
      id: 'mapbox://styles/mapbox/streets-v12',
      name: 'Streets',
      url: 'mapbox.streets'
    },
    {
      id: 'mapbox://styles/mapbox/satellite-v9',
      name: 'Satellite',
      url: 'mapbox.satellite'
    },
    {
      id: 'mapbox://styles/mapbox/outdoors-v12',
      name: 'Outdoors',
      url: 'mapbox.outdoors'
    }
  ];

  private static downloadControllers = new Map<string, AbortController>();
  private static progressCallbacks = new Map<string, (progress: DownloadProgress) => void>();

  /**
   * Download map tiles for a specific region
   */
  static async downloadRegion(
    region: DownloadRegion,
    styleId: string = 'mapbox://styles/mapbox/streets-v12',
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<boolean> {
    if (!this.mapboxAccessToken) {
      throw new Error('Mapbox access token not configured');
    }

    // Check if already downloading
    if (this.downloadControllers.has(region.id)) {
      console.warn(`Download already in progress for region: ${region.name}`);
      return false;
    }

    // Create abort controller for this download
    const controller = new AbortController();
    this.downloadControllers.set(region.id, controller);

    if (onProgress) {
      this.progressCallbacks.set(region.id, onProgress);
    }

    try {
      console.log(`Starting download for region: ${region.name}`);
      
      // Calculate all tile coordinates for the region
      const tileCoordinates = this.calculateTileCoordinates(region);
      const totalTiles = tileCoordinates.length;

      console.log(`Region ${region.name}: ${totalTiles} tiles to download (zoom ${region.zoomLevels.min}-${region.zoomLevels.max})`);

      const progress: DownloadProgress = {
        regionId: region.id,
        totalTiles,
        downloadedTiles: 0,
        failedTiles: 0,
        speed: 0,
        estimatedTimeRemaining: 0,
        bytesDownloaded: 0,
        bytesPerSecond: 0
      };

      const startTime = Date.now();
      let lastProgressTime = startTime;
      let downloadedSinceLastUpdate = 0;

      // Download tiles in batches to avoid overwhelming the server
      const batchSize = 8;
      const batches = this.chunkArray(tileCoordinates, batchSize);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        // Check if download was cancelled
        if (controller.signal.aborted) {
          break;
        }

        // Download tiles in current batch concurrently
        const batchPromises = batch.map(coord => 
          this.downloadTile(coord, styleId, controller.signal)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        // Update progress
        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          const coord = batch[i];

          if (result.status === 'fulfilled' && result.value) {
            progress.downloadedTiles++;
            progress.bytesDownloaded += result.value.data.byteLength;
            downloadedSinceLastUpdate++;
          } else {
            progress.failedTiles++;
            console.warn(`Failed to download tile ${coord.z}/${coord.x}/${coord.y}:`, 
              result.status === 'rejected' ? result.reason : 'Unknown error');
          }

          progress.currentTile = coord;
        }

        // Calculate speed and ETA
        const now = Date.now();
        const timeDelta = (now - lastProgressTime) / 1000;
        if (timeDelta >= 1) { // Update speed every second
          progress.speed = downloadedSinceLastUpdate / timeDelta;
          progress.bytesPerSecond = progress.bytesDownloaded / ((now - startTime) / 1000);
          
          const remainingTiles = totalTiles - progress.downloadedTiles;
          progress.estimatedTimeRemaining = progress.speed > 0 ? remainingTiles / progress.speed : 0;

          lastProgressTime = now;
          downloadedSinceLastUpdate = 0;

          // Report progress
          if (onProgress) {
            onProgress({ ...progress });
          }
        }

        // Small delay between batches to be respectful to the API
        if (batchIndex < batches.length - 1) {
          await this.delay(100);
        }
      }

      // Final progress update
      if (onProgress) {
        progress.speed = progress.downloadedTiles / ((Date.now() - startTime) / 1000);
        progress.estimatedTimeRemaining = 0;
        onProgress({ ...progress });
      }

      // Update region metadata
      const updatedRegion: DownloadRegion = {
        ...region,
        downloadedAt: new Date().toISOString(),
        tileCount: progress.downloadedTiles,
        size: progress.bytesDownloaded
      };

      await OfflineStorageService.storeRegion(updatedRegion);

      const success = progress.failedTiles === 0;
      console.log(`Download completed for region: ${region.name}. Success: ${success}, Downloaded: ${progress.downloadedTiles}/${totalTiles}, Failed: ${progress.failedTiles}`);

      return success;

    } catch (error) {
      console.error(`Error downloading region ${region.name}:`, error);
      return false;
    } finally {
      // Cleanup
      this.downloadControllers.delete(region.id);
      this.progressCallbacks.delete(region.id);
    }
  }

  /**
   * Cancel an ongoing download
   */
  static cancelDownload(regionId: string): void {
    const controller = this.downloadControllers.get(regionId);
    if (controller) {
      controller.abort();
      console.log(`Download cancelled for region: ${regionId}`);
    }
  }

  /**
   * Download a single map tile
   */
  private static async downloadTile(
    coord: TileCoordinate,
    styleId: string,
    signal: AbortSignal
  ): Promise<CachedTile | null> {
    try {
      // Get the Mapbox tileset ID from the style
      const style = this.MAP_STYLES.find(s => s.id === styleId);
      const tilesetId = style?.url || 'mapbox.streets';

      // Construct tile URL
      const tileUrl = `${this.MAPBOX_TILES_URL}/${tilesetId}/${coord.z}/${coord.x}/${coord.y}@2x.png?access_token=${this.mapboxAccessToken}`;

      // Download tile
      const response = await fetch(tileUrl, { signal });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();

      // Create cached tile object
      const cachedTile: CachedTile = {
        url: tileUrl,
        data: arrayBuffer,
        timestamp: Date.now()
      };

      // Store in cache
      await OfflineStorageService.cacheTile(tileUrl, arrayBuffer);

      return cachedTile;

    } catch (error) {
      if ((error as Error).name === 'AbortError') {
        console.log(`Tile download aborted: ${coord.z}/${coord.x}/${coord.y}`);
      } else {
        console.error(`Error downloading tile ${coord.z}/${coord.x}/${coord.y}:`, error);
      }
      return null;
    }
  }

  /**
   * Calculate all tile coordinates for a region and zoom levels
   */
  private static calculateTileCoordinates(region: DownloadRegion): TileCoordinate[] {
    const coordinates: TileCoordinate[] = [];

    for (let z = region.zoomLevels.min; z <= region.zoomLevels.max; z++) {
      const minTile = this.deg2tile(region.bounds.north, region.bounds.west, z);
      const maxTile = this.deg2tile(region.bounds.south, region.bounds.east, z);

      for (let x = Math.min(minTile.x, maxTile.x); x <= Math.max(minTile.x, maxTile.x); x++) {
        for (let y = Math.min(minTile.y, maxTile.y); y <= Math.max(minTile.y, maxTile.y); y++) {
          coordinates.push({ x, y, z });
        }
      }
    }

    return coordinates;
  }

  /**
   * Convert latitude/longitude to tile coordinates
   */
  private static deg2tile(lat: number, lon: number, zoom: number): { x: number; y: number } {
    const latRad = (lat * Math.PI) / 180;
    const n = Math.pow(2, zoom);
    const x = Math.floor(((lon + 180) / 360) * n);
    const y = Math.floor(((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2) * n);
    
    return { x, y };
  }

  /**
   * Split array into chunks
   */
  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Simple delay utility
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get available map styles
   */
  static getMapStyles(): MapStyle[] {
    return [...this.MAP_STYLES];
  }

  /**
   * Estimate download size for a region
   */
  static estimateDownloadSize(region: DownloadRegion): {
    tileCount: number;
    estimatedSizeMB: number;
  } {
    const tileCoordinates = this.calculateTileCoordinates(region);
    const tileCount = tileCoordinates.length;
    
    // Rough estimate: average tile size is ~50KB
    const avgTileSizeKB = 50;
    const estimatedSizeMB = (tileCount * avgTileSizeKB) / 1024;

    return {
      tileCount,
      estimatedSizeMB: Math.round(estimatedSizeMB * 100) / 100
    };
  }

  /**
   * Check if a region is currently being downloaded
   */
  static isDownloading(regionId: string): boolean {
    return this.downloadControllers.has(regionId);
  }

  /**
   * Get all currently downloading regions
   */
  static getActiveDownloads(): string[] {
    return Array.from(this.downloadControllers.keys());
  }

  /**
   * Verify cached tiles for a region
   */
  static async verifyRegionCache(region: DownloadRegion): Promise<{
    totalTiles: number;
    cachedTiles: number;
    missingTiles: TileCoordinate[];
  }> {
    const tileCoordinates = this.calculateTileCoordinates(region);
    const missingTiles: TileCoordinate[] = [];
    let cachedTiles = 0;

    for (const coord of tileCoordinates) {
      const style = this.MAP_STYLES[0]; // Use default style for verification
      const tileUrl = `${this.MAPBOX_TILES_URL}/${style.url}/${coord.z}/${coord.x}/${coord.y}@2x.png?access_token=${this.mapboxAccessToken}`;
      
      const cachedTile = await OfflineStorageService.getCachedTile(tileUrl);
      if (cachedTile) {
        cachedTiles++;
      } else {
        missingTiles.push(coord);
      }
    }

    return {
      totalTiles: tileCoordinates.length,
      cachedTiles,
      missingTiles
    };
  }

  /**
   * Clean up old cached tiles
   */
  static async cleanupOldTiles(maxAgeHours: number = 24 * 7): Promise<number> {
    // This would require implementing a cleanup mechanism in OfflineStorageService
    // For now, just return 0
    console.log(`Cleanup requested for tiles older than ${maxAgeHours} hours`);
    return 0;
  }

  /**
   * Get download statistics
   */
  static async getDownloadStats(): Promise<{
    totalRegions: number;
    totalTiles: number;
    totalSizeMB: number;
    lastDownload?: string;
  }> {
    const regions = await OfflineStorageService.getDownloadedRegions();
    const totalRegions = regions.length;
    const totalTiles = regions.reduce((sum, region) => sum + (region.tileCount || 0), 0);
    const totalSizeBytes = regions.reduce((sum, region) => sum + (region.size || 0), 0);
    const totalSizeMB = Math.round((totalSizeBytes / (1024 * 1024)) * 100) / 100;
    
    const lastDownload = regions
      .filter(r => r.downloadedAt)
      .sort((a, b) => new Date(b.downloadedAt!).getTime() - new Date(a.downloadedAt!).getTime())[0]?.downloadedAt;

    return {
      totalRegions,
      totalTiles,
      totalSizeMB,
      lastDownload
    };
  }
}

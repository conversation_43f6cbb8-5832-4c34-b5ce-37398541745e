// Test utilities for authentication debugging
export const logAuthState = () => {
  console.log('=== AUTH STATE DEBUG ===');
  console.log('Current URL:', window.location.href);
  console.log('Local Storage keys:', Object.keys(localStorage));
  
  // Check for Supabase auth tokens
  const authKeys = Object.keys(localStorage).filter(key => 
    key.includes('supabase') || key.includes('auth')
  );
  
  authKeys.forEach(key => {
    console.log(`${key}:`, localStorage.getItem(key)?.substring(0, 100) + '...');
  });
  
  console.log('=== END AUTH DEBUG ===');
};

// Call this in browser console to debug auth issues
(window as any).debugAuth = logAuthState;

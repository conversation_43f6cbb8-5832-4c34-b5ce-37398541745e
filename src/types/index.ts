import type { Polygon } from 'geojson';

export interface User {
  id: string;
  email: string;
  role: 'authority' | 'community';
  profile?: {
    name?: string;
    organization?: string;
  };
}

// Database schema based types that match component usage
export interface ProtectedZone {
  id: string;
  type: 'Feature';
  name: string;
  description?: string;
  geometry: Polygon;
  properties: {
    name: string;
    description?: string;
    effectiveFrom?: string;
    effectiveTo?: string;
    enforcementNotes?: string;
    createdBy?: string;
    createdAt?: string;
    updatedAt?: string;
  };
  status?: 'active' | 'inactive' | 'draft';
  created_by?: string;
  created_at?: string;
  updated_at?: string;
  // Component compatibility properties
  protection_level?: 'no_take' | 'restricted' | 'seasonal';
  regulations?: string;
  is_active?: boolean;
}

export interface BreachEvent {
  id?: string;
  zone_id: string;
  user_id: string;
  location: {
    coordinates: [number, number]; // [longitude, latitude]
  };
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  synced?: boolean;
  notes?: string;
  // Joined data from related tables
  protected_zones?: {
    name: string;
    protection_level?: string;
  };
  profiles?: {
    full_name: string;
  };
  // Legacy interface support for backwards compatibility
  userId?: string;
  zoneId?: string;
  coordinates?: [number, number];
  timestamp?: string;
  deviceId?: string;
}

export interface DownloadRegion {
  id: string;
  name: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  zoomLevels: {
    min: number;
    max: number;
  };
  downloadedAt?: string;
  size?: number; // in bytes
  tileCount?: number;
}

export interface AppState {
  user: User | null;
  isOnline: boolean;
  downloadedRegions: DownloadRegion[];
  currentLocation: [number, number] | null;
  isLocationTracking: boolean;
  error: string | null;
  isLoading: boolean;
}

export interface MapboxStyle {
  version: number;
  sources: Record<string, any>;
  layers: any[];
  glyphs?: string;
  sprite?: string;
}

export interface CachedTile {
  url: string;
  data: ArrayBuffer;
  timestamp: number;
}

export interface SyncQueue {
  breachEvents: BreachEvent[];
  lastSyncAttempt?: string;
}

import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import type { ProtectedZone } from '../../types';
import './MapboxMap.css';

// You'll need to set your Mapbox access token
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.your_mapbox_token_here';

interface MapboxMapProps {
  zones: ProtectedZone[];
  selectedZone?: ProtectedZone | null;
  onZoneSelect?: (zone: ProtectedZone | null) => void;
  userRole: 'authority' | 'community';
  currentLocation?: [number, number] | null;
  isLocationTracking?: boolean;
  onZoneCreated?: (zone: ProtectedZone) => void;
  onZoneUpdated?: (zone: ProtectedZone) => void;
  onZoneDeleted?: (zoneId: string) => void;
}

export const MapboxMap: React.FC<MapboxMapProps> = ({
  zones,
  selectedZone,
  onZoneSelect,
  userRole,
  currentLocation,
  isLocationTracking,
  onZoneCreated
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const draw = useRef<MapboxDraw | null>(null);
  const userLocationMarker = useRef<mapboxgl.Marker | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  useEffect(() => {
    if (!mapContainer.current) return;

    // Initialize map
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/satellite-streets-v12',
      center: [119.135, 18.165], // San Vicente, Palawan, Philippines
      zoom: 12,
      attributionControl: false
    });

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

    // Add attribution control
    map.current.addControl(new mapboxgl.AttributionControl({
      compact: true
    }), 'bottom-right');

    // Initialize drawing tools for authority users
    if (userRole === 'authority') {
      draw.current = new MapboxDraw({
        displayControlsDefault: false,
        controls: {
          polygon: true,
          trash: true
        },
        defaultMode: 'simple_select'
      });
      
      map.current.addControl(draw.current, 'top-left');

      // Handle draw events
      map.current.on('draw.create', handleDrawCreate);
      map.current.on('draw.update', handleDrawUpdate);
      map.current.on('draw.delete', handleDrawDelete);
    }

    map.current.on('load', () => {
      setIsMapLoaded(true);
      addZoneLayers();
    });

    map.current.on('click', 'zones-layer', handleZoneClick);

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  useEffect(() => {
    if (isMapLoaded && map.current) {
      updateZonesData();
    }
  }, [zones, isMapLoaded]);

  useEffect(() => {
    updateUserLocation();
  }, [currentLocation, isLocationTracking]);

  const addZoneLayers = () => {
    if (!map.current) return;

    // Add zones source
    map.current.addSource('zones', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: zones.map(zone => ({
          type: 'Feature' as const,
          geometry: zone.geometry,
          properties: zone.properties,
          id: zone.id
        }))
      }
    });

    // Add zone fill layer
    map.current.addLayer({
      id: 'zones-layer',
      type: 'fill',
      source: 'zones',
      paint: {
        'fill-color': [
          'case',
          ['==', ['get', 'id'], selectedZone?.id || ''],
          '#ff4444',
          '#ff6b6b'
        ],
        'fill-opacity': 0.3
      }
    });

    // Add zone outline layer
    map.current.addLayer({
      id: 'zones-outline',
      type: 'line',
      source: 'zones',
      paint: {
        'line-color': [
          'case',
          ['==', ['get', 'id'], selectedZone?.id || ''],
          '#cc0000',
          '#ff4444'
        ],
        'line-width': 2
      }
    });
  };

  const updateZonesData = () => {
    if (!map.current) return;

    const source = map.current.getSource('zones') as mapboxgl.GeoJSONSource;
    if (source) {
      source.setData({
        type: 'FeatureCollection',
        features: zones.map(zone => ({
          type: 'Feature' as const,
          geometry: zone.geometry,
          properties: zone.properties,
          id: zone.id
        }))
      });
    }
  };

  const updateUserLocation = () => {
    if (!map.current) return;

    // Remove existing marker
    if (userLocationMarker.current) {
      userLocationMarker.current.remove();
    }

    // Add new marker if we have location
    if (currentLocation && isLocationTracking) {
      const el = document.createElement('div');
      el.className = 'user-location-marker';
      el.innerHTML = '📍';

      userLocationMarker.current = new mapboxgl.Marker(el)
        .setLngLat(currentLocation)
        .addTo(map.current);
    }
  };

  const handleZoneClick = (e: mapboxgl.MapMouseEvent) => {
    if (!onZoneSelect) return;

    const features = e.features;
    if (features && features.length > 0) {
      const clickedZone = features[0] as any;
      const zone = zones.find(z => z.id === clickedZone.properties.id);
      onZoneSelect(zone || null);
    }
  };

  const handleDrawCreate = (e: any) => {
    if (!onZoneCreated) return;

    const feature = e.features[0];
    const newZone: ProtectedZone = {
      id: `zone_${Date.now()}`,
      type: 'Feature',
      name: `Protected Zone ${zones.length + 1}`,
      geometry: feature.geometry,
      properties: {
        name: `Protected Zone ${zones.length + 1}`,
        description: 'New protected marine area',
        effectiveFrom: new Date().toISOString(),
        createdBy: 'current_user_id', // This should come from auth context
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    onZoneCreated(newZone);

    // Remove from draw to prevent duplicate
    if (draw.current) {
      draw.current.delete(feature.id);
    }
  };

  const handleDrawUpdate = (e: any) => {
    // Handle zone updates
    console.log('Draw updated:', e);
  };

  const handleDrawDelete = (e: any) => {
    // Handle zone deletion
    console.log('Draw deleted:', e);
  };

  return (
    <div className="mapbox-map">
      <div ref={mapContainer} className="map-container" />
      
      {userRole === 'community' && (
        <div className="map-info-panel">
          <div className="location-info">
            {isLocationTracking ? (
              currentLocation ? (
                <span className="location-active">
                  📍 Location: {currentLocation[1].toFixed(4)}, {currentLocation[0].toFixed(4)}
                </span>
              ) : (
                <span className="location-searching">📍 Searching for location...</span>
              )
            ) : (
              <span className="location-inactive">📍 Location tracking disabled</span>
            )}
          </div>
          
          <div className="zones-info">
            <span>🚫 {zones.length} protected zones loaded</span>
          </div>
        </div>
      )}

      {selectedZone && (
        <div className="zone-info-popup">
          <div className="popup-content">
            <h3>{selectedZone.properties?.name || selectedZone.name}</h3>
            <p>{selectedZone.properties?.description || selectedZone.description}</p>
            <p><strong>Effective from:</strong> {selectedZone.properties?.effectiveFrom ? new Date(selectedZone.properties.effectiveFrom).toLocaleDateString() : 'N/A'}</p>
            {selectedZone.properties?.enforcementNotes && (
              <p><strong>Notes:</strong> {selectedZone.properties.enforcementNotes}</p>
            )}
            <button onClick={() => onZoneSelect?.(null)} className="close-popup">
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MapboxMap;

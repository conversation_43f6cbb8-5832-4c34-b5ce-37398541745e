.location-tracker {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.location-tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.location-tracker-header h2 {
  margin: 0;
  color: var(--dark-gray);
  font-size: 1.25rem;
}

.tracking-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--success-green);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.tracking-status {
  font-size: 14px;
  font-weight: 500;
}

.tracking-status.active {
  color: var(--success-green);
}

.tracking-status.inactive {
  color: var(--medium-gray);
}

.location-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.location-stat-card {
  background-color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.location-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin: 0;
}

.location-stat-label {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: var(--spacing-xs) 0 0 0;
}

.current-location {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.current-location h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1rem;
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.location-icon {
  color: var(--success-green);
}

.location-coordinates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.coordinate-item {
  text-align: center;
}

.coordinate-label {
  font-size: 12px;
  color: var(--medium-gray);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.coordinate-value {
  font-family: monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--dark-gray);
}

.location-accuracy {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--light-gray);
  border-radius: 4px;
  font-size: 12px;
}

.accuracy-good {
  color: var(--success-green);
}

.accuracy-fair {
  color: var(--warning-orange);
}

.accuracy-poor {
  color: var(--danger-red);
}

.zone-alerts {
  flex: 1;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.zone-alerts-header {
  background-color: var(--light-gray);
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zone-alerts-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-gray);
}

.alert-count {
  background-color: var(--danger-red);
  color: var(--white);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.alerts-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.alert-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.alert-item:hover {
  background-color: var(--light-gray);
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item.active {
  background-color: #fef8f8;
  border-left: 4px solid var(--danger-red);
}

.alert-item.warning {
  background-color: #fff9f5;
  border-left: 4px solid var(--warning-orange);
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.alert-zone {
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 14px;
}

.alert-time {
  font-size: 11px;
  color: var(--medium-gray);
}

.alert-message {
  font-size: 13px;
  color: var(--dark-gray);
  margin-bottom: var(--spacing-xs);
}

.alert-distance {
  font-size: 12px;
  color: var(--medium-gray);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.distance-icon {
  color: var(--primary-blue);
}

.no-alerts {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--medium-gray);
}

.no-alerts h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--success-green);
}

.no-alerts p {
  margin: 0;
  font-size: 14px;
}

.tracking-history {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.tracking-history h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1rem;
  color: var(--dark-gray);
}

.history-controls {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.time-filter {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--white);
}

.export-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-blue);
  color: var(--white);
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.history-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--light-gray);
  border-radius: 4px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-weight: 600;
  color: var(--primary-blue);
  font-size: 14px;
}

.summary-label {
  font-size: 11px;
  color: var(--medium-gray);
  margin-top: var(--spacing-xs);
}

.location-permissions {
  background-color: var(--warning-orange);
  color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.location-permissions h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
}

.location-permissions p {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 12px;
}

.enable-location-btn {
  background-color: var(--white);
  color: var(--warning-orange);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

/* Enhanced tracking status styles */
.tracking-details {
  margin-top: var(--spacing-xs);
  font-size: 0.9rem;
  opacity: 0.8;
}

.tracking-details p {
  margin: 0.2rem 0;
}

/* Notification status styles */
.enable-notifications-btn {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.enable-notifications-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* Data management styles */
.data-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--light-blue);
  color: var(--dark-gray);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 120px;
}

.action-btn:hover {
  background: var(--ocean-blue);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Location history styles */
.location-history {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.location-history h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--dark-gray);
  font-size: 0.95rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
  background: var(--light-blue);
  border-radius: 4px;
  font-size: 0.85rem;
  align-items: center;
}

.history-item .time {
  font-weight: 500;
  color: var(--ocean-blue);
  white-space: nowrap;
}

.history-item .coords {
  font-family: monospace;
  color: var(--dark-gray);
}

.history-item .accuracy {
  color: var(--medium-gray);
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Enhanced location status grid */
.location-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

@media (max-width: 768px) {
  .location-tracker {
    padding: var(--spacing-sm);
  }
  
  .location-tracker-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
  
  .tracking-toggle {
    justify-content: center;
  }
  
  .location-stats {
    grid-template-columns: 1fr;
  }
  
  .location-coordinates {
    grid-template-columns: 1fr;
  }
  
  .history-controls {
    flex-direction: column;
  }
  
  .history-summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .location-status {
    grid-template-columns: 1fr;
  }
  
  .data-actions {
    flex-direction: column;
  }
  
  .action-btn {
    min-width: unset;
  }
}
